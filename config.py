"""Configuration for the equipment inventory management application."""
import os
import logging
from datetime import datetime
from pathlib import Path

# Application information
APP_NAME = "InventoryTracker"
APP_VERSION = "1.0.0"
def get_app_icon():
    """Get application icon, with fallback options."""
    # Check for optimized PNG first
    png_icon = Path("resources/app_icon.png")
    if png_icon.exists() and png_icon.stat().st_size < 100*1024:  # Less than 100KB
        return str(png_icon)
    
    # Fallback to SVG if PNG doesn't exist or is too large
    svg_icon = Path("resources/app_icon.svg")
    if svg_icon.exists():
        return str(svg_icon)
    
    # No icon found
    return None

APP_ICON = get_app_icon()

# Robust database and logging configuration: always use user-writable directory and fallback if needed
import getpass

def get_user_writable_dir():
    """Get user writable directory using secure Path objects."""
    local_app_data = os.getenv("LOCALAPPDATA")
    if local_app_data:
        base_dir = Path(local_app_data)
    else:
        base_dir = Path.home()
    
    user_dir = base_dir / APP_NAME
    return str(user_dir)

def get_log_path():
    """Get log file path with validation."""
    # Allow override via environment variable
    env_log_path = os.getenv("INVENTORY_LOG_PATH")
    if env_log_path:
        log_path = Path(env_log_path)
        # Validate the path is safe
        if validate_file_path(log_path, allow_create=True):
            return str(log_path)
        else:
            logger.warning(f"Invalid log path in environment variable: {env_log_path}")
    
    user_dir = Path(get_user_writable_dir())
    user_dir.mkdir(parents=True, exist_ok=True)
    return str(user_dir / "inventory_app.log")

def get_db_path():
    """Get database file path with validation."""
    # Allow override via environment variable
    env_db_path = os.getenv("INVENTORY_DB_PATH")
    if env_db_path:
        db_path = Path(env_db_path)
        # Validate the path is safe
        if validate_file_path(db_path, allow_create=True):
            return str(db_path)
        else:
            logger.warning(f"Invalid database path in environment variable: {env_db_path}")
    
    user_dir = Path(get_user_writable_dir())
    user_dir.mkdir(parents=True, exist_ok=True)
    return str(user_dir / "inventory.db")

def validate_file_path(file_path, allow_create=False):
    """
    Validate file path for security and accessibility.
    
    Args:
        file_path (Path): Path object to validate
        allow_create (bool): Whether to allow non-existent files
        
    Returns:
        bool: True if path is valid and safe
    """
    try:
        # Convert to absolute path and resolve
        abs_path = file_path.resolve()
        
        # Check if path tries to escape user directory
        user_base = Path(get_user_writable_dir()).resolve()
        temp_base = Path.home() / "AppData" / "Local" / "Temp"
        
        # Allow paths within user directory or temp
        if not (str(abs_path).startswith(str(user_base)) or 
                str(abs_path).startswith(str(temp_base))):
            return False
        
        # Check parent directory exists or can be created
        parent = abs_path.parent
        if not parent.exists():
            if allow_create:
                try:
                    parent.mkdir(parents=True, exist_ok=True)
                except OSError:
                    return False
            else:
                return False
        
        # Check if file exists (if it should)
        if not allow_create and not abs_path.exists():
            return False
            
        return True
        
    except (OSError, ValueError):
        return False

# Environment-configurable paths
LOG_PATH = get_log_path()
DB_PATH = get_db_path()

# Environment-configurable settings
DEBUG_MODE = os.getenv("INVENTORY_DEBUG", "false").lower() == "true"
MAX_LOG_SIZE_MB = int(os.getenv("INVENTORY_MAX_LOG_SIZE_MB", "5"))
LOG_BACKUP_COUNT = int(os.getenv("INVENTORY_LOG_BACKUP_COUNT", "3"))

LOG_LEVEL = logging.INFO  # Changed from DEBUG to reduce log file size
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# Logging initialization moved to bottom of file to avoid duplication

# Date format for display
DATE_FORMAT = '%Y-%m-%d'

# Default top-up percent for fluids (can be overridden per-fluid)
DEFAULT_TOP_UP_PERCENT = 10

# Current fiscal year (format: YYYY-YY)
current_year = datetime.now().year
next_year = current_year + 1
CURRENT_FISCAL_YEAR = f"{current_year}-{str(next_year)[-2:]}"

# UI style
UI_STYLE = """
    QMainWindow {
        background-color: #f5f5f5;
    }
    QTabWidget::pane {
        border: 1px solid #cccccc;
        background-color: white;
        border-radius: 5px;
    }
    QTabBar::tab {
        background-color: #e0e0e0;
        color: #333333;
        padding: 8px 16px;
        border: 1px solid #cccccc;
        border-bottom: none;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        margin-right: 2px;
    }
    QTabBar::tab:selected {
        background-color: white;
        border-bottom-color: white;
    }
    QTabBar::tab:hover:!selected {
        background-color: #eeeeee;
    }
    QGroupBox {
        font-weight: bold;
        border: 1px solid #cccccc;
        border-radius: 5px;
        margin-top: 1.5ex;
        padding-top: 1.5ex;
    }
    QGroupBox::title {
        subcontrol-origin: margin;
        subcontrol-position: top center;
        padding: 0 5px;
    }
    QPushButton {
        background-color: #0078d7;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #005a9e;
    }
    QPushButton:disabled {
        background-color: #cccccc;
        color: #888888;
    }
    QLineEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QComboBox, QTextEdit {
        border: 1px solid #cccccc;
        border-radius: 4px;
        padding: 4px;
        background-color: white;
    }
    QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QComboBox:focus, QTextEdit:focus {
        border: 1px solid #0078d7;
    }
    QLineEdit:read-only {
        background-color: #f0f0f0;
    }
    QScrollArea {
        border: none;
    }
"""

# Initialize logging
def init_logging():
    """Initialize application logging with rotation."""
    from logging.handlers import RotatingFileHandler
    
    # Create rotating file handler (configurable size and backup count)
    file_handler = RotatingFileHandler(
        LOG_PATH, 
        maxBytes=MAX_LOG_SIZE_MB*1024*1024,  # Configurable MB
        backupCount=LOG_BACKUP_COUNT
    )
    file_handler.setLevel(LOG_LEVEL)
    file_handler.setFormatter(logging.Formatter(LOG_FORMAT))
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)  # Only show warnings/errors on console
    console_handler.setFormatter(logging.Formatter(LOG_FORMAT))
    
    # Configure root logger
    logging.basicConfig(
        level=LOG_LEVEL,
        handlers=[file_handler, console_handler]
    )
    
    # Set more restrictive log levels for some loggers
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)

# Call init_logging when importing this module
init_logging()

# BA Number Configuration
import re

BA_NUMBER_VALIDATION_REGEX = r'^[0-9]{2}[A-Z]{1}[0-9A-Z]+$'  # Matches patterns like 86R3124A, 95R5843H, 09E020651H
REQUIRE_BA_NUMBER = False  # Make BA number optional during transition
BA_NUMBER_DISPLAY_FORMAT = "BA: {ba_number}"

def validate_ba_number(ba_number):
    """
    Validate BA number format and content.
    
    Args:
        ba_number (str): The BA number to validate
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if not ba_number and not REQUIRE_BA_NUMBER:
        return True, None
    
    if not ba_number and REQUIRE_BA_NUMBER:
        return False, "BA number is required"
    
    ba_number = ba_number.strip().upper()
    
    # Check length (minimum 4 characters)
    if len(ba_number) < 4:
        return False, "BA number must be at least 4 characters long"
    
    # Check maximum length (reasonable limit)
    if len(ba_number) > 15:
        return False, "BA number cannot exceed 15 characters"
    
    # Check format using regex
    if not re.match(BA_NUMBER_VALIDATION_REGEX, ba_number):
        return False, "BA number format is invalid. Expected format: ##X#####... (e.g., 86R3124A)"
    
    return True, None

def sanitize_ba_number(ba_number):
    """
    Sanitize BA number input by removing invalid characters and normalizing format.
    
    Args:
        ba_number (str): Raw BA number input
        
    Returns:
        str: Sanitized BA number
    """
    if not ba_number:
        return ""
    
    # Remove whitespace and convert to uppercase
    ba_number = ba_number.strip().upper()
    
    # Remove non-alphanumeric characters
    ba_number = re.sub(r'[^0-9A-Z]', '', ba_number)
    
    return ba_number

# Maintenance configuration
MAINTENANCE_CRITICAL_DAYS = 7     # Under 7 days = Critical
MAINTENANCE_WARNING_DAYS = 30     # Under 30 days = Warning
MAINTENANCE_UPCOMING_DAYS = 90    # Under 3 months (90 days) = Upcoming

# Maintenance categories and their periods
MAINTENANCE_CATEGORIES = {
    'TM-1': {'months': 6, 'display': 'TM-1 (Half Yearly)'},
    'TM-2': {'months': 12, 'display': 'TM-2 (Yearly)'},
    'Yearly': {'months': 12, 'display': 'Yearly'},
    'Monthly': {'months': 1, 'display': 'Monthly'}
}

# Maintenance types for each category
MAINTENANCE_TYPES = {
    'TM-1': ['TM-I', 'Half Yearly Service', 'Semi-Annual Check'],
    'TM-2': ['TM-II', 'Annual Service', 'Yearly Overhaul'],
    'Yearly': ['Annual Check', 'Yearly Inspection', 'Annual Maintenance'],
    'Monthly': ['Monthly Service', 'Monthly Check', 'Monthly Inspection']
}

# Discard criteria configuration - Updated with official military rules
DISCARD_CRITERIA_RULES = {
    # Military Equipment - Exact names as provided
    'MOTOR CYCLE (HH/RE)': {'years': 15, 'kms': 75000, 'hours': 0},
    'TATA SAFARI': {'years': 9, 'kms': 100000, 'hours': 0},
    'MAHINDRA SCORPIO': {'years': 15, 'kms': 100000, 'hours': 0},
    'MARUTI GYPSY': {'years': 9, 'kms': 100000, 'hours': 0},
    'ALS (AMB)': {'years': 9, 'kms': 60000, 'hours': 0},
    'ALS GS VEH': {'years': 9, 'kms': 100000, 'hours': 0},
    'TATA 2.5 TON': {'years': 9, 'kms': 100000, 'hours': 0},
    'TATA 2.5 TON 2KL WB': {'years': 9, 'kms': 100000, 'hours': 0},
    'ARMY BUS': {'years': 15, 'kms': 110000, 'hours': 0},
    'ALS LRV': {'years': 9, 'kms': 60000, 'hours': 0},
    'ALS 5 KL WB': {'years': 9, 'kms': 60000, 'hours': 0, 'logic': 'LATER'},  # Special: "and" logic
    'DOZER': {'years': 18, 'kms': 0, 'hours': 2000},
    'DOZER D-80 A-12': {'years': 18, 'kms': 0, 'hours': 2000},
    'JCB ALL TYPES': {'years': 18, 'kms': 0, 'hours': 2000},
    'SSL': {'years': 18, 'kms': 0, 'hours': 2000},
    'GENERATOR SET UPTO 5KVA': {'years': 9, 'kms': 0, 'hours': 7000},
    'GENERATOR 5KVA TO 30 KVA': {'years': 12, 'kms': 0, 'hours': 7000},
    'GENERATOR 30 KVA ABOVE': {'years': 15, 'kms': 0, 'hours': 7000},  # Fixed: was 7000 kms, now 7000 hrs
    'BMP-I, BMP-II AND AERV': {'years': 35, 'kms': 7900, 'hours': 0},
}

DISCARD_CRITERIA_PRIORITY = [
    # Most specific patterns first (longest names)
    'TATA 2.5 TON 2KL WB',
    'GENERATOR 30 KVA ABOVE',
    'GENERATOR 5KVA TO 30 KVA', 
    'GENERATOR SET UPTO 5KVA',
    'BMP-I, BMP-II AND AERV',
    'MOTOR CYCLE (HH/RE)',
    'DOZER D-80 A-12',
    'MAHINDRA SCORPIO',
    'JCB ALL TYPES',
    'TATA 2.5 TON',
    'TATA SAFARI',
    'MARUTI GYPSY',
    'ALS 5 KL WB',
    'ALS GS VEH',
    'ALS (AMB)',
    'ALS LRV',
    'ARMY BUS',
    'DOZER',
    'SSL',
]

# Discard criteria evaluation settings
DISCARD_WARNING_THRESHOLD = 0.9  # Warn when 90% of criteria reached
DISCARD_CRITICAL_THRESHOLD = 1.0  # Critical when criteria exceeded
DISCARD_NOTIFICATION_ENABLED = True
DISCARD_NOTIFICATION_DAYS = [30, 7, 1]  # Days before discard to send notifications