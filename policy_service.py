"""
Policy Service Module
Handles vehicle class policy management and application.
"""
import logging
from datetime import datetime
from policy_models import VehicleClassPolicy, PolicyCondition
# Import directly from models.py
import models
Equipment = models.Equipment

logger = logging.getLogger('policy_service')

# Cache for policies to improve performance and ensure consistency
_policy_cache = {
    'all_policies': None,
    'policy_by_id': {},
    'policy_by_make_type': {}
}

def normalize_make_type(make_and_type):
    """Normalize make and type value for consistent matching.
    
    Removes leading/trailing whitespace and replaces newlines with spaces.
    
    Args:
        make_and_type (str): Make and type value to normalize
        
    Returns:
        str: Normalized make and type value
    """
    if not make_and_type:
        return ''
    # Remove leading/trailing whitespace and replace newlines with spaces
    return ' '.join(make_and_type.strip().splitlines())

def clear_policy_cache():
    """Clear all cached policy data to ensure fresh data is loaded after updates."""
    global _policy_cache
    _policy_cache = {
        'all_policies': None,
        'policy_by_id': {},
        'policy_by_make_type': {}
    }
    logger.info("Policy cache cleared")



def create_or_update_policy(make_and_type, conditions_data, policy_id=None, created_by=None):
    """
    Create or update a vehicle class policy with its conditions.
    
    Args:
        make_and_type (str): Vehicle make and type
        conditions_data (list): List of condition dictionaries
        policy_id (int, optional): Policy ID for update
        created_by (str, optional): User who created the policy
    
    Returns:
        int: Policy ID
    """
    try:
        # Validate input data
        if not make_and_type:
            logger.error("Make and type is required for policy creation")
            return None
        
        # Create or update policy
        policy = VehicleClassPolicy(
            policy_id=policy_id,
            make_and_type=make_and_type,
            created_by=created_by,
            is_active=1
        )
        policy_id = policy.save()
        
        # Delete existing conditions if updating
        if policy_id:
            PolicyCondition.delete_by_policy(policy_id)
        
        # Create new conditions
        for condition_data in conditions_data:
            condition = PolicyCondition(
                policy_id=policy_id,
                condition_type=condition_data.get('condition_type'),
                years_threshold=condition_data.get('years_threshold'),
                km_threshold=condition_data.get('km_threshold'),
                hours_threshold=condition_data.get('hours_threshold'),
                logic_type=condition_data.get('logic_type', PolicyCondition.EARLIER),
                reference_condition_id=condition_data.get('reference_condition_id'),
                is_relative=condition_data.get('is_relative', 0)
            )
            condition.save()
        
        # Clear policy cache to ensure fresh data is loaded after update
        clear_policy_cache()
        logger.info(f"Policy for {make_and_type} {'updated' if policy_id else 'created'} successfully")
        return policy_id
        
    except Exception as e:
        logger.error(f"Error creating/updating policy: {e}")
        return None


def get_policy_with_conditions(policy_id=None, make_and_type=None, force_refresh=False):
    """
    Get a policy with all its conditions.
    Uses cache for better performance unless force_refresh is True.
    
    Args:
        policy_id (int, optional): Policy ID
        make_and_type (str, optional): Make and type
        force_refresh (bool): If True, bypass cache and get fresh data
    
    Returns:
        dict: Policy with conditions
    """
    try:
        global _policy_cache
        
        # Check cache first if not forcing refresh
        if not force_refresh:
            if policy_id and policy_id in _policy_cache['policy_by_id']:
                return _policy_cache['policy_by_id'][policy_id]
            
            if make_and_type:
                normalized_make_type = normalize_make_type(make_and_type)
                if normalized_make_type in _policy_cache['policy_by_make_type']:
                    return _policy_cache['policy_by_make_type'][normalized_make_type]
        
        # Cache miss or forced refresh, get fresh data
        policy = None
        if policy_id:
            policy = VehicleClassPolicy.get_by_id(policy_id)
        elif make_and_type:
            policy = VehicleClassPolicy.get_by_make_type(make_and_type)
        
        if not policy:
            return None
        
        # Get conditions
        conditions = PolicyCondition.get_by_policy(policy.get('policy_id'))
        
        # Format response
        result = {
            'policy': policy,
            'conditions': conditions
        }
        
        # Cache the result
        policy_id = policy.get('policy_id')
        _policy_cache['policy_by_id'][policy_id] = result
        
        # Normalize make_and_type for consistent cache keys
        normalized_make_type = normalize_make_type(policy.get('make_and_type', ''))
        _policy_cache['policy_by_make_type'][normalized_make_type] = result
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting policy: {e}")
        return None


def delete_policy(policy_id):
    """
    Delete a policy and its conditions.
    
    Args:
        policy_id (int): Policy ID
    
    Returns:
        bool: Success status
    """
    try:
        # Delete conditions first
        PolicyCondition.delete_by_policy(policy_id)
        
        # Delete policy
        VehicleClassPolicy.delete(policy_id)
        
        logger.info(f"Policy {policy_id} deleted successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error deleting policy: {e}")
        return False


def get_all_policies(force_refresh=False):
    """
    Get all policies with their conditions.
    Uses cache for better performance unless force_refresh is True.
    
    Args:
        force_refresh (bool): If True, bypass cache and get fresh data
        
    Returns:
        list: List of policies with conditions
    """
    try:
        global _policy_cache
        
        # Return cached policies if available and not forcing refresh
        if _policy_cache['all_policies'] is not None and not force_refresh:
            logger.debug("Returning cached policies")
            return _policy_cache['all_policies']
            
        # Cache miss or forced refresh, get fresh data
        policies = VehicleClassPolicy.get_all()
        result = []
        
        for policy in policies:
            policy_id = policy.get('policy_id')
            conditions = PolicyCondition.get_by_policy(policy_id)
            
            policy_data = {
                'policy': policy,
                'conditions': conditions
            }
            
            result.append(policy_data)
            
            # Also cache individual policy data
            _policy_cache['policy_by_id'][policy_id] = policy_data
            
            # Normalize make_and_type for consistent cache keys
            normalized_make_type = normalize_make_type(policy.get('make_and_type', ''))
            _policy_cache['policy_by_make_type'][normalized_make_type] = policy_data
        
        # Cache all policies
        _policy_cache['all_policies'] = result
        logger.debug(f"Cached {len(result)} policies")
        
        return result
        
    except Exception as e:
        logger.error(f"Error getting all policies: {e}")
        return []


def get_policy_for_equipment(equipment_id):
    """
    Get policy applicable to specific equipment.
    
    Args:
        equipment_id (int): Equipment ID
    
    Returns:
        dict: Policy with conditions
    """
    try:
        equipment = Equipment.get_by_id(equipment_id)
        if not equipment:
            logger.warning(f"Equipment not found: {equipment_id}")
            return None
        
        make_and_type = equipment.get('make_and_type')
        if not make_and_type:
            return None
        
        return get_policy_with_conditions(make_and_type=make_and_type)
        
    except Exception as e:
        logger.error(f"Error getting policy for equipment: {e}")
        return None


def evaluate_equipment_against_policy(equipment, policy_data):
    """
    Evaluate equipment against a policy.
    
    Args:
        equipment (dict): Equipment data
        policy_data (dict): Policy with conditions
    
    Returns:
        dict: Evaluation results with status for each condition
    """
    try:
        if not equipment or not policy_data:
            return None
        
        conditions = policy_data.get('conditions', [])
        
        # Extract equipment metrics
        vintage_years = float(equipment.get('vintage_years') or 0)
        meterage_kms = float(equipment.get('meterage_kms') or 0)
        hours_run = float(equipment.get('hours_run_total') or 0)
        
        results = {
            'equipment_id': equipment.get('equipment_id'),
            'make_and_type': equipment.get('make_and_type'),
            'ba_number': equipment.get('ba_number'),
            'current_vintage': vintage_years,
            'current_kms': meterage_kms,
            'current_hours': hours_run,
            'conditions': {}
        }
        
        # Evaluate each condition
        for condition in conditions:
            condition_type = condition.get('condition_type')
            years_threshold = float(condition.get('years_threshold') or 0)
            km_threshold = float(condition.get('km_threshold') or 0)
            hours_threshold = float(condition.get('hours_threshold') or 0)
            logic_type = condition.get('logic_type', PolicyCondition.EARLIER)
            
            # Check if condition is met based on logic type
            meets_years = years_threshold > 0 and vintage_years >= years_threshold
            meets_kms = km_threshold > 0 and meterage_kms >= km_threshold
            meets_hours = hours_threshold > 0 and hours_run >= hours_threshold
            
            condition_met = False
            
            if logic_type == PolicyCondition.EARLIER:
                # Any threshold met is sufficient
                condition_met = meets_years or meets_kms or meets_hours
            elif logic_type == PolicyCondition.LATER:
                # All applicable thresholds must be met
                applicable_count = 0
                met_count = 0
                
                if years_threshold > 0:
                    applicable_count += 1
                    if meets_years:
                        met_count += 1
                
                if km_threshold > 0:
                    applicable_count += 1
                    if meets_kms:
                        met_count += 1
                
                if hours_threshold > 0:
                    applicable_count += 1
                    if meets_hours:
                        met_count += 1
                
                condition_met = applicable_count > 0 and met_count == applicable_count
            elif logic_type == PolicyCondition.EXACT:
                # Specific threshold must be met exactly
                if years_threshold > 0:
                    condition_met = meets_years
                elif km_threshold > 0:
                    condition_met = meets_kms
                elif hours_threshold > 0:
                    condition_met = meets_hours
            
            # Add to results
            results['conditions'][condition_type] = {
                'met': condition_met,
                'years_threshold': years_threshold,
                'km_threshold': km_threshold,
                'hours_threshold': hours_threshold,
                'meets_years': meets_years,
                'meets_kms': meets_kms,
                'meets_hours': meets_hours
            }
        
        # Determine overall status
        if condition_type == PolicyCondition.DISCARD in results['conditions'] and results['conditions'][PolicyCondition.DISCARD]['met']:
            results['status'] = "Meeting Discard Criteria"
            results['status_color'] = "#e74c3c"  # Red
        else:
            results['status'] = "Normal"
            results['status_color'] = "#27ae60"  # Green
        
        return results
        
    except Exception as e:
        logger.error(f"Error evaluating equipment against policy: {e}")
        return None


def create_default_policies():
    """
    Create default policies based on existing discard criteria.
    
    Returns:
        int: Number of policies created
    """
    try:
        from models import DiscardCriteria
        import database
        
        # Get unique make and types with criteria
        conn = database.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT e.make_and_type, dc.criteria_years, dc.criteria_kms
                FROM discard_criteria dc
                JOIN equipment e ON dc.equipment_id = e.equipment_id
                WHERE e.make_and_type IS NOT NULL
            """)
            unique_criteria = cursor.fetchall()
        finally:
            conn.close()
        
        count = 0
        
        for criteria in unique_criteria:
            make_and_type = criteria.get('make_and_type')
            years = criteria.get('criteria_years')
            kms = criteria.get('criteria_kms')
            
            # Skip if already has policy
            existing = VehicleClassPolicy.get_by_make_type(make_and_type)
            if existing:
                continue
            
            # Create policy with discard condition
            conditions = [{
                'condition_type': PolicyCondition.DISCARD,
                'years_threshold': years,
                'km_threshold': kms,
                'hours_threshold': None,
                'logic_type': PolicyCondition.EARLIER
            }]
            
            policy_id = create_or_update_policy(make_and_type, conditions, created_by="system")
            if policy_id:
                count += 1
        
        logger.info(f"Created {count} default policies")
        return count
        
    except Exception as e:
        logger.error(f"Error creating default policies: {e}")
        return 0


def get_equipment_policy_status(equipment_data):
    """
    Get policy status for specific equipment.
    
    Args:
        equipment_data (dict): Dictionary containing equipment data with at least:
            - make_and_type: Equipment make and type
            - vintage_years: Current age in years
            - meterage_kms: Current kilometers
            - hours_run_total: Current hours run (optional)
    
    Returns:
        dict: Policy status information including:
            - policy_name: Name of the applied policy
            - policy_id: ID of the applied policy
            - conditions: Dict of all conditions with their status
            - meets_discard: Boolean indicating if equipment meets discard criteria
    """
    try:
        # Get the policy for this equipment type
        policy_data = get_policy_with_conditions(make_and_type=equipment_data.get('make_and_type'))
        
        if not policy_data:
            return None
        
        # Evaluate equipment against the policy
        evaluation = evaluate_equipment_against_policy(equipment_data, policy_data)
        
        if not evaluation:
            return None
            
        # Prepare result with appropriate information
        result = {
            'policy_name': policy_data.get('make_and_type', 'Unknown Policy'),
            'policy_id': policy_data.get('policy_id'),
            'conditions': evaluation.get('conditions', {}),
            'meets_discard': False
        }
        
        # Check if equipment meets discard criteria
        if PolicyCondition.DISCARD in evaluation.get('conditions', {}):
            discard_condition = evaluation['conditions'][PolicyCondition.DISCARD]
            result['meets_discard'] = discard_condition.get('met', False)
            
        return result
            
    except Exception as e:
        logger.error(f"Error getting equipment policy status: {e}")
        return None


def ensure_tables_exist():
    """
    Ensure all policy-related tables exist in the database.
    
    Returns:
        bool: Success status
    """
    try:
        VehicleClassPolicy.create_tables()
        PolicyCondition.create_tables()
        logger.info("Policy tables verified")
        return True
    except Exception as e:
        logger.error(f"Error ensuring policy tables exist: {e}")
        return False


def create_default_policies_from_config():
    """
    Create default policies based on the configuration rules in config.py.
    
    Returns:
        dict: Summary of policies created
    """
    try:
        import config
        
        created_count = 0
        updated_count = 0
        skipped_count = 0
        
        # Get the discard criteria rules from config
        rules = getattr(config, 'DISCARD_CRITERIA_RULES', {})
        
        if not rules:
            logger.warning("No discard criteria rules found in config")
            return {'created': 0, 'updated': 0, 'skipped': 0, 'error': 'No rules in config'}
        
        for make_type, criteria in rules.items():
            try:
                # Check if policy already exists
                existing_policy = VehicleClassPolicy.get_by_make_type(make_type)
                
                # Prepare condition data for DISCARD condition
                conditions_data = [{
                    'condition_type': PolicyCondition.DISCARD,
                    'years_threshold': criteria.get('years', 0),
                    'km_threshold': criteria.get('kms', 0),
                    'hours_threshold': criteria.get('hours', 0),
                    'logic_type': PolicyCondition.EARLIER,  # Default to "whichever is earlier"
                    'reference_condition_id': None,
                    'is_relative': 0
                }]
                
                if existing_policy:
                    # Update existing policy
                    policy_id = create_or_update_policy(
                        make_and_type=make_type,
                        conditions_data=conditions_data,
                        policy_id=existing_policy.get('policy_id'),
                        created_by='system'
                    )
                    if policy_id:
                        updated_count += 1
                        logger.info(f"Updated policy for {make_type}")
                    else:
                        skipped_count += 1
                        logger.warning(f"Failed to update policy for {make_type}")
                else:
                    # Create new policy
                    policy_id = create_or_update_policy(
                        make_and_type=make_type,
                        conditions_data=conditions_data,
                        policy_id=None,
                        created_by='system'
                    )
                    if policy_id:
                        created_count += 1
                        logger.info(f"Created policy for {make_type}: {criteria}")
                    else:
                        skipped_count += 1
                        logger.warning(f"Failed to create policy for {make_type}")
                        
            except Exception as e:
                logger.error(f"Error processing policy for {make_type}: {e}")
                skipped_count += 1
        
        result = {
            'created': created_count,
            'updated': updated_count,
            'skipped': skipped_count,
            'total_rules': len(rules)
        }
        
        logger.info(f"Policy creation summary: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Error creating default policies from config: {e}")
        return {'created': 0, 'updated': 0, 'skipped': 0, 'error': str(e)}
