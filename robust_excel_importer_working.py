"""
Working Excel Importer - Actually saves data to production database
"""

import pandas as pd
import logging
import re
from datetime import datetime, date
from typing import Dict, Tuple, Any, Optional

logger = logging.getLogger('working_excel_importer')

class RobustExcelImporter:
    """Working Excel importer that saves to production database."""
    
    def __init__(self, staging_db_path: str = 'staging.db'):
        self.staging_db_path = staging_db_path
        self.session_id = f"import_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"Initialized WorkingExcelImporter with session {self.session_id}")
    
    def initialize_staging(self) -> bool:
        """Initialize the production database."""
        try:
            # Initialize production database instead of staging
            import database
            database.init_db()
            
            # Ensure equipment table exists
            import sqlite3
            conn = sqlite3.connect('inventory.db')
            cursor = conn.cursor()
            
            # Create equipment table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS equipment (
                    equipment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    serial_number TEXT,
                    make_and_type TEXT,
                    ba_number TEXT,
                    units_held INTEGER DEFAULT 1,
                    vintage_years REAL DEFAULT 0,
                    meterage_kms REAL DEFAULT 0,
                    km_hrs_run_previous_month REAL DEFAULT 0,
                    km_hrs_run_current_month REAL DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    remarks TEXT,
                    date_of_commission TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            logger.info("Production database and tables initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize production database: {e}")
            return False
    
    def process_excel_file(self, file_path: str) -> Tuple[bool, Dict[str, Any]]:
        """Process Excel file and save to production database."""
        try:
            logger.info(f"Processing Excel file with data extraction: {file_path}")
            
            # Read Excel file
            excel_file = pd.ExcelFile(file_path)
            
            stats = {
                'total_equipment': 0,
                'total_fluids': 0,
                'total_maintenance': 0,
                'total_overhauls': 0,
                'total_conditioning': 0,
                'total_batteries': 0,
                'total_repairs': 0,
                'total_discard_criteria': 0,
                'total_conditioning': 0,
                'total_skipped': 0,
                'session_id': self.session_id,
                'sheets_processed': []
            }
            
            # Process each sheet and extract real data
            for sheet_name in excel_file.sheet_names:
                logger.info(f"Processing sheet: {sheet_name}")
                
                try:
                    # Read sheet with header detection
                    df = self._read_sheet_with_headers(excel_file, sheet_name)
                    
                    if df is None:
                        logger.warning(f"Could not read sheet: {sheet_name}")
                        continue
                    
                    # Extract and save equipment data
                    equipment_count = self._extract_and_save_equipment(df, sheet_name)
                    stats['total_equipment'] += equipment_count
                    
                    # Extract and save fluids data for the equipment in this sheet
                    fluids_count = self._extract_and_save_fluids(df, sheet_name)
                    stats['total_fluids'] += fluids_count
                    
                    # Extract and save maintenance data for the equipment in this sheet
                    maintenance_count = self._extract_and_save_maintenance(df, sheet_name)
                    stats['total_maintenance'] += maintenance_count
                    
                    # Extract and save overhaul data for the equipment in this sheet
                    overhaul_count = self._extract_and_save_overhauls(df, sheet_name)
                    stats['total_overhauls'] += overhaul_count
                    
                    # Extract and save conditioning data for the equipment in this sheet
                    conditioning_count = self._extract_and_save_conditioning(df, sheet_name)
                    stats['total_conditioning'] += conditioning_count
                    
                    # Extract and save battery data for the equipment in this sheet
                    battery_count = self._extract_and_save_batteries(df, sheet_name)
                    stats['total_batteries'] += battery_count
                    
                    stats['sheets_processed'].append(sheet_name)
                    
                except Exception as e:
                    logger.error(f"Error processing sheet {sheet_name}: {e}")
                    stats['total_skipped'] += 1
            
            logger.info(f"Excel processing completed: {stats}")
            return True, stats
            
        except Exception as e:
            logger.error(f"Error processing Excel file: {e}")
            return False, {'error': str(e)}
    
    def _read_sheet_with_headers(self, excel_file: pd.ExcelFile, sheet_name: str) -> Optional[pd.DataFrame]:
        """Read sheet with proper header detection."""
        # Try different header configurations
        for header_config in [[0, 1, 2], [0, 1], 0]:
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name, header=header_config)
                
                # Flatten multi-level columns
                if isinstance(df.columns, pd.MultiIndex):
                    df.columns = [' '.join(str(col).strip() for col in cols if str(col) != 'nan') 
                                for cols in df.columns.values]
                
                # Check if this looks like a valid data frame
                if len(df.columns) > 3 and len(df) > 0:
                    return df
                    
            except Exception:
                continue
        
        return None
    
    def _extract_and_save_equipment(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract equipment data from DataFrame and save to database."""
        from models import Equipment
        
        equipment_count = 0
        
        # Find equipment-related columns
        col_map = self._map_equipment_columns(df.columns)
        
        logger.info(f"Column mapping for {sheet_name}: {col_map}")
        
        for index, row in df.iterrows():
            try:
                # Extract equipment data
                equipment_data = self._extract_equipment_data(row, col_map, sheet_name)
                
                # Filter out invalid records (validation rows, headers, etc.)
                if equipment_data and equipment_data.get('make_and_type') and self._is_valid_equipment_record(equipment_data):
                    # Save directly to database using SQL
                    import sqlite3
                    import config
                    conn = sqlite3.connect(config.DB_PATH)
                    cursor = conn.cursor()
                    
                    try:
                        cursor.execute('''
                            INSERT INTO equipment (
                                serial_number, make_and_type, ba_number, units_held,
                                vintage_years, meterage_kms, hours_run_total,
                                km_hrs_run_previous_month, km_hrs_run_current_month, 
                                hours_run_previous_month, hours_run_current_month,
                                is_active, remarks, date_of_commission
                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            equipment_data.get('serial_number', f"AUTO_{equipment_count + 1}"),
                            equipment_data['make_and_type'],
                            equipment_data.get('ba_number'),
                            equipment_data.get('units_held', 1),
                            equipment_data.get('vintage_years', 0),
                            equipment_data.get('meterage_kms', 0),
                            equipment_data.get('hours_run_total', 0),
                            equipment_data.get('km_hrs_run_previous_month', 0),
                            equipment_data.get('km_hrs_run_current_month', 0),
                            equipment_data.get('hours_run_previous_month', 0),
                            equipment_data.get('hours_run_current_month', 0),
                            1,  # is_active
                            f"Imported from {sheet_name}",
                            equipment_data.get('date_of_commission')
                        ))
                        
                        conn.commit()
                        equipment_count += 1
                        logger.info(f"Saved equipment {equipment_count}: {equipment_data['make_and_type']}")
                        
                    except Exception as e:
                        logger.error(f"Error saving equipment: {e}")
                    finally:
                        conn.close()
                
            except Exception as e:
                logger.error(f"Error processing row {index} in {sheet_name}: {e}")
                continue
        
        return equipment_count
    
    def _map_equipment_columns(self, columns: list) -> Dict[str, str]:
        """Map DataFrame columns to equipment fields."""
        col_map = {}
        
        logger.info(f"Original columns: {columns}")
        
        # Equipment field mappings with regex patterns for better matching
        mappings = {
            'make_and_type': [
                r'make.*type', r'make\s*&\s*type', r'make\s*and\s*type',
                r'equipment.*type', r'vehicle.*type', r'make', r'type', 
                r'equipment', r'vehicle', r'description'
            ],
            'ba_number': [
                r'ba.*no', r'ba.*number', r'ba\s*no', r'ba\s*number',
                r'army.*no', r'registration.*no', r'reg.*no', r'vehicle.*no'
            ],
            'serial_number': [
                r'serial.*number', r'serial.*no', r'serial', r's.*no', r'sl.*no'
            ],
            'units_held': [
                r'units', r'qty', r'quantity', r'count', r'units.*held',
                r'units.*available', r'held'
            ],
            'meterage_kms': [
                r'km.*run', r'kms.*run', r'meterage', r'km', r'kms', 
                r'kilometers', r'mileage', r'total.*km'
            ],
            'hours_run_total': [
                r'hrs.*run', r'hours.*run', r'hrs\s*run', r'hours\s*run',
                r'runtime', r'engine.*hours', r'total.*hours', r'running.*hours', 
                r'operating.*hours', r'hrs$', r'hours$'
            ],
            'vintage_years': [
                r'vintage', r'age.*years', r'years.*old', r'years.*service',
                r'year.*manufacture', r'manufacture.*year'
            ],
            'date_of_commission': [
                r'date.*commission', r'commission.*date', r'date.*release',
                r'date.*rel', r'release.*date', r'induction.*date'
            ]
        }
        
        for field, patterns in mappings.items():
            for col in columns:
                col_str = str(col).lower()
                # Clean up the column string but preserve key words
                col_clean = re.sub(r'unnamed:\s*\d+_level_\d+', '', col_str)
                col_clean = re.sub(r'\s+', ' ', col_clean).strip()
                
                for pattern in patterns:
                    if re.search(pattern, col_clean):
                        col_map[field] = col
                        logger.info(f"Mapped '{field}' to column: '{col}' (matched pattern: {pattern})")
                        break
                if field in col_map:
                    break
        
        logger.info(f"Final column mapping: {col_map}")
        return col_map
    
    def _normalize_column_name(self, col_name: str) -> str:
        """Normalize column name for matching."""
        if not col_name:
            return ''
        return re.sub(r'[^a-z0-9]', '', str(col_name).lower().replace(' ', ''))
    
    def _extract_equipment_data(self, row: pd.Series, col_map: Dict[str, str], sheet_name: str) -> Dict[str, Any]:
        """Extract equipment data from a DataFrame row."""
        data = {}
        
        # Debug logging
        logger.info(f"Extracting data for row in {sheet_name}")
        logger.info(f"Column mapping: {col_map}")
        
        # Extract mapped fields
        for field, col_name in col_map.items():
            if col_name in row.index:
                value = row[col_name]
                logger.info(f"Field '{field}' -> Column '{col_name}' -> Value: '{value}'")
                if pd.notna(value) and str(value).strip() and str(value).strip() not in ['-', 'NA', 'N/A']:
                    if field in ['meterage_kms', 'hours_run_total', 'vintage_years', 'units_held']:
                        data[field] = self._parse_numeric(value)
                        logger.info(f"Set numeric {field} = {data[field]}")
                    elif field == 'date_of_commission':
                        data[field] = self._parse_date(value)
                    else:
                        data[field] = str(value).strip()
                        logger.info(f"Set {field} = '{data[field]}'")
            else:
                logger.warning(f"Column '{col_name}' not found in row index for field '{field}'")
        
        # If no make_and_type found in mapped columns, try to find it in any column
        if 'make_and_type' not in data:
            logger.info(f"No make_and_type found in mapped data, searching all columns...")
            equipment_keywords = [
                'tatra', 'truck', 'vehicle', 'generator', 'jcb', 'dozer', 
                'bmp', 'tank', 'bulldozer', 'excavator', 'crane', 'trailer',
                'jeep', 'bus', 'ambulance', 'recovery', 'workshop', 'water',
                'fuel', 'cargo', 'personnel', 'command', 'communication'
            ]
            
            # Look for equipment names in any column
            for col in row.index:
                value = row[col]
                if pd.notna(value) and str(value).strip():
                    val_str = str(value).strip()
                    val_lower = val_str.lower()
                    
                    # Check if this looks like an equipment name
                    if (len(val_str) > 3 and 
                        (any(keyword in val_lower for keyword in equipment_keywords) or
                         re.search(r'\d+x\d+', val_lower) or  # Pattern like 6X6, 8X8
                         re.search(r'\d+\s*cyl', val_lower) or  # Pattern like 12 cyl
                         'cabin' in val_lower)):
                        
                        # Clean up the equipment name
                        cleaned_name = re.sub(r'\s+', ' ', val_str)
                        data['make_and_type'] = cleaned_name
                        logger.info(f"Found equipment name in column '{col}': {cleaned_name}")
                        break
            
            # If still no equipment type found, look for any meaningful text
            if 'make_and_type' not in data:
                logger.info(f"Still no equipment name found, trying any meaningful text...")
                for col in row.index:
                    value = row[col]
                    if pd.notna(value) and str(value).strip():
                        val_str = str(value).strip()
                        # Use the first non-trivial text value
                        if len(val_str) > 5 and not val_str.lower() in ['unnamed', 'nan', 'null']:
                            data['make_and_type'] = val_str
                            logger.info(f"Using first meaningful text as equipment name: {val_str}")
                            break
            
            # Fallback: use sheet name if still no equipment type found
            if 'make_and_type' not in data:
                data['make_and_type'] = f"Equipment from {sheet_name}"
                logger.warning(f"Using fallback name: {data['make_and_type']}")
        
        logger.info(f"Final extracted data: {data}")
        return data
    
    def _is_valid_equipment_record(self, equipment_data: Dict[str, Any]) -> bool:
        """Check if the equipment record is valid and not a header/validation row."""
        make_and_type = equipment_data.get('make_and_type', '').strip().upper()
        ba_number = equipment_data.get('ba_number', '').strip().upper()
        serial_number = equipment_data.get('serial_number', '').strip().upper()
        
        # Skip records with validation text
        invalid_indicators = [
            'NOT ASSIGNED',
            'ALL DATA ARE CORRECT',
            'ALL DATA CORRECT',
            'HEADER',
            'TOTAL',
            'SUMMARY',
            'VALIDATION',
            'CHECK',
            'VERIFY'
        ]
        
        # Check make_and_type for invalid indicators
        for indicator in invalid_indicators:
            if indicator in make_and_type:
                logger.info(f"Skipping invalid record: {make_and_type}")
                return False
        
        # Check BA number for invalid indicators
        for indicator in invalid_indicators:
            if indicator in ba_number:
                logger.info(f"Skipping invalid record with BA: {ba_number}")
                return False
        
        # Check serial number for invalid indicators
        for indicator in invalid_indicators:
            if indicator in serial_number:
                logger.info(f"Skipping invalid record with Serial: {serial_number}")
                return False
        
        # Skip records where make_and_type is just the sheet name (fallback case)
        if make_and_type.startswith('EQUIPMENT FROM'):
            # Check if it's a meaningful equipment type or just a generic fallback
            meaningful_keywords = [
                'BMP', 'TATRA', 'TRUCK', 'TANK', 'VEHICLE', 'GENERATOR', 
                'JCB', 'DOZER', 'CRANE', 'TRAILER', 'ALS', 'MSS', 'CT'
            ]
            has_meaningful_content = any(keyword in make_and_type for keyword in meaningful_keywords)
            if not has_meaningful_content:
                logger.info(f"Skipping generic fallback record: {make_and_type}")
                return False
        
        # Additional validation: skip if all numeric fields are zero
        numeric_fields = ['meterage_kms', 'vintage_years']
        all_zero = True
        for field in numeric_fields:
            if equipment_data.get(field, 0) > 0:
                all_zero = False
                break
        
        if all_zero and not ba_number:  # No BA number and all zeros is suspicious
            logger.info(f"Skipping record with all zero values and no BA number: {make_and_type}")
            return False
        
        return True
    
    def _extract_and_save_fluids(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract fluids data from DataFrame and save to database."""
        fluids_count = 0
        
        # Find fluid-related columns
        fluid_types = ['ENG OIL', 'HYDRAULIC FLUID', 'COOLANT', 'GREASE ENGINE CLUTCH', 
                      'ROAD WHEELS ARMS', 'BRAKE FLUID', 'TRANSMISSION OIL']
        
        # Map equipment data first to get equipment IDs
        equipment_col_map = self._map_equipment_columns(df.columns)
        
        for index, row in df.iterrows():
            try:
                # Get equipment data to link fluids to equipment
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                
                if not equipment_data or not equipment_data.get('make_and_type') or not self._is_valid_equipment_record(equipment_data):
                    continue
                
                # Find the equipment ID by matching the data
                equipment_id = self._find_equipment_id(equipment_data)
                if not equipment_id:
                    continue
                
                # Extract fluid data for each fluid type
                for fluid_type in fluid_types:
                    fluid_data = self._extract_fluid_data(row, fluid_type, equipment_id)
                    
                    if fluid_data and fluid_data.get('capacity_ltrs_kg', 0) > 0:
                        # Save fluid data to database
                        if self._save_fluid_to_db(fluid_data):
                            fluids_count += 1
                            logger.info(f"Saved fluid: {fluid_type} for equipment ID {equipment_id}")
                
            except Exception as e:
                logger.error(f"Error processing fluids for row {index} in {sheet_name}: {e}")
                continue
        
        return fluids_count
    
    def _extract_fluid_data(self, row: pd.Series, fluid_type: str, equipment_id: int) -> Dict[str, Any]:
        """Extract fluid data for a specific fluid type from a row."""
        fluid_data = {
            'equipment_id': equipment_id,
            'fluid_type': fluid_type,
            'accounting_unit': 'Ltr',
            'capacity_ltrs_kg': 0.0,
            'addl_10_percent_top_up': 0.0,
            'grade': None,
            'periodicity_km': 0,
            'periodicity_hrs': 0,
            'periodicity_months': 0,
            'date_of_change': None
        }
        
        # Find columns for this fluid type
        for col in row.index:
            col_str = str(col).upper()
            
            if fluid_type in col_str:
                if 'CAPACITY' in col_str and 'LTRS/KG' in col_str:
                    fluid_data['capacity_ltrs_kg'] = self._parse_numeric(row[col])
                elif 'ADDL 10% TOP UP' in col_str:
                    fluid_data['addl_10_percent_top_up'] = self._parse_numeric(row[col])
                elif 'GRADE' in col_str:
                    if pd.notna(row[col]) and str(row[col]).strip():
                        fluid_data['grade'] = str(row[col]).strip()
                elif 'PERIODICITY' in col_str:
                    # Try to extract periodicity
                    periodicity_val = self._parse_numeric(row[col])
                    if 'KM' in col_str:
                        fluid_data['periodicity_km'] = periodicity_val
                    elif 'HRS' in col_str:
                        fluid_data['periodicity_hrs'] = periodicity_val
                    elif 'MONTH' in col_str:
                        fluid_data['periodicity_months'] = periodicity_val
                elif 'DT OF CHANGE' in col_str:
                    fluid_data['date_of_change'] = self._parse_date(row[col])
        
        return fluid_data if fluid_data['capacity_ltrs_kg'] > 0 else None
    
    def _find_equipment_id(self, equipment_data: Dict[str, Any]) -> Optional[int]:
        """Find equipment ID by matching equipment data."""
        import sqlite3
        import config
        
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Try to find by BA number first (most specific)
            if equipment_data.get('ba_number'):
                cursor.execute('SELECT equipment_id FROM equipment WHERE ba_number = ?', 
                             (equipment_data['ba_number'],))
                result = cursor.fetchone()
                if result:
                    conn.close()
                    return result[0]
            
            # Try to find by make_and_type and serial_number
            if equipment_data.get('make_and_type') and equipment_data.get('serial_number'):
                cursor.execute('SELECT equipment_id FROM equipment WHERE make_and_type = ? AND serial_number = ?', 
                             (equipment_data['make_and_type'], equipment_data['serial_number']))
                result = cursor.fetchone()
                if result:
                    conn.close()
                    return result[0]
            
            # Try to find by make_and_type only (less specific)
            if equipment_data.get('make_and_type'):
                cursor.execute('SELECT equipment_id FROM equipment WHERE make_and_type = ? ORDER BY equipment_id LIMIT 1', 
                             (equipment_data['make_and_type'],))
                result = cursor.fetchone()
                if result:
                    conn.close()
                    return result[0]
            
            conn.close()
            return None
            
        except Exception as e:
            logger.error(f"Error finding equipment ID: {e}")
            return None
    
    def _save_fluid_to_db(self, fluid_data: Dict[str, Any]) -> bool:
        """Save fluid data to database."""
        import sqlite3
        import config
        
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Check if fluid already exists for this equipment and type
            cursor.execute('''
                SELECT fluid_id FROM fluids 
                WHERE equipment_id = ? AND fluid_type = ?
            ''', (fluid_data['equipment_id'], fluid_data['fluid_type']))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing fluid
                cursor.execute('''
                    UPDATE fluids SET
                        accounting_unit = ?,
                        capacity_ltrs_kg = ?,
                        addl_10_percent_top_up = ?,
                        grade = ?,
                        periodicity_km = ?,
                        periodicity_hrs = ?,
                        periodicity_months = ?,
                        date_of_change = ?
                    WHERE fluid_id = ?
                ''', (
                    fluid_data['accounting_unit'],
                    fluid_data['capacity_ltrs_kg'],
                    fluid_data['addl_10_percent_top_up'],
                    fluid_data['grade'],
                    fluid_data['periodicity_km'],
                    fluid_data['periodicity_hrs'],
                    fluid_data['periodicity_months'],
                    fluid_data['date_of_change'],
                    existing[0]
                ))
            else:
                # Insert new fluid
                cursor.execute('''
                    INSERT INTO fluids (
                        equipment_id, fluid_type, accounting_unit,
                        capacity_ltrs_kg, addl_10_percent_top_up, grade,
                        periodicity_km, periodicity_hrs, periodicity_months,
                        date_of_change
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    fluid_data['equipment_id'],
                    fluid_data['fluid_type'],
                    fluid_data['accounting_unit'],
                    fluid_data['capacity_ltrs_kg'],
                    fluid_data['addl_10_percent_top_up'],
                    fluid_data['grade'],
                    fluid_data['periodicity_km'],
                    fluid_data['periodicity_hrs'],
                    fluid_data['periodicity_months'],
                    fluid_data['date_of_change']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving fluid to database: {e}")
            return False
    
    def _extract_and_save_maintenance(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract maintenance data from DataFrame and save to database."""
        maintenance_count = 0
        
        # Find equipment-related columns
        equipment_col_map = self._map_equipment_columns(df.columns)
        
        for index, row in df.iterrows():
            try:
                # Get equipment data to link maintenance to equipment
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                
                if not equipment_data or not equipment_data.get('make_and_type') or not self._is_valid_equipment_record(equipment_data):
                    continue
                
                # Find the equipment ID
                equipment_id = self._find_equipment_id(equipment_data)
                if not equipment_id:
                    continue
                
                # Extract maintenance data for TM-1 and TM-2 (match UI categories)
                maintenance_types = [
                    ('TM-1', 'TM - I'),
                    ('TM-2', 'TM - II')
                ]
                
                for category, pattern in maintenance_types:
                    maintenance_data = self._extract_maintenance_data(row, pattern, category, equipment_id)
                    
                    if maintenance_data:
                        # Save maintenance data to database
                        if self._save_maintenance_to_db(maintenance_data):
                            maintenance_count += 1
                            logger.info(f"Saved maintenance: {category} for equipment ID {equipment_id}")
                
            except Exception as e:
                logger.error(f"Error processing maintenance for row {index} in {sheet_name}: {e}")
                continue
        
        return maintenance_count
    
    def _extract_maintenance_data(self, row: pd.Series, pattern: str, category: str, equipment_id: int) -> Optional[Dict[str, Any]]:
        """Extract maintenance data for a specific maintenance type from a row."""
        maintenance_data = {
            'equipment_id': equipment_id,
            'maintenance_category': category,
            'maintenance_type': f"{category} Maintenance",
            'done_date': None,
            'due_date': None,
            'status': 'scheduled',
            'completion_notes': f'Imported from Excel sheet: {pattern}'
        }
        
        # Find columns for this maintenance type
        done_date = None
        due_date = None
        
        for col in row.index:
            col_str = str(col).upper()
            
            if pattern in col_str:
                if 'DONE' in col_str:
                    done_date = self._parse_date(row[col])
                elif 'DUE' in col_str:
                    due_date = self._parse_date(row[col])
        
        # Set dates and status
        if done_date:
            maintenance_data['done_date'] = done_date
            maintenance_data['status'] = 'completed'
        
        if due_date:
            maintenance_data['due_date'] = due_date
        
        # Only return if we have at least one date
        return maintenance_data if (done_date or due_date) else None
    
    def _save_maintenance_to_db(self, maintenance_data: Dict[str, Any]) -> bool:
        """Save maintenance data to database."""
        import sqlite3
        import config
        
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Check if maintenance already exists for this equipment and category
            cursor.execute('''
                SELECT maintenance_id FROM maintenance 
                WHERE equipment_id = ? AND maintenance_category = ?
                ORDER BY maintenance_id DESC LIMIT 1
            ''', (maintenance_data['equipment_id'], maintenance_data['maintenance_category']))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing maintenance
                cursor.execute('''
                    UPDATE maintenance SET
                        maintenance_type = ?,
                        done_date = ?,
                        due_date = ?,
                        status = ?,
                        completion_notes = ?
                    WHERE maintenance_id = ?
                ''', (
                    maintenance_data['maintenance_type'],
                    maintenance_data['done_date'],
                    maintenance_data['due_date'],
                    maintenance_data['status'],
                    maintenance_data['completion_notes'],
                    existing[0]
                ))
            else:
                # Insert new maintenance
                cursor.execute('''
                    INSERT INTO maintenance (
                        equipment_id, maintenance_type, maintenance_category,
                        done_date, due_date, status, completion_notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    maintenance_data['equipment_id'],
                    maintenance_data['maintenance_type'],
                    maintenance_data['maintenance_category'],
                    maintenance_data['done_date'],
                    maintenance_data['due_date'],
                    maintenance_data['status'],
                    maintenance_data['completion_notes']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving maintenance to database: {e}")
            return False
    
    def _parse_numeric(self, value: Any) -> float:
        """Parse numeric value with error handling."""
        if pd.isna(value):
            return 0.0
        
        try:
            # Handle string values
            if isinstance(value, str):
                # Remove common non-numeric characters
                cleaned = re.sub(r'[^\d.-]', '', value.replace(',', ''))
                return float(cleaned) if cleaned else 0.0
            return float(value)
        except (ValueError, TypeError):
            return 0.0
    
    def _parse_date(self, value: Any) -> Optional[str]:
        """Parse date value with error handling."""
        if pd.isna(value):
            return None
        
        try:
            if isinstance(value, (pd.Timestamp, datetime, date)):
                if isinstance(value, pd.Timestamp):
                    return value.strftime('%Y-%m-%d')
                elif isinstance(value, datetime):
                    return value.date().isoformat()
                elif isinstance(value, date):
                    return value.isoformat()
            
            # Handle string dates
            if isinstance(value, str):
                # Try common date formats
                for fmt in ['%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d', '%d.%m.%Y']:
                    try:
                        parsed_date = datetime.strptime(value.strip(), fmt)
                        return parsed_date.date().isoformat()
                    except ValueError:
                        continue
            
            return None
        except Exception:
            return None

    def _extract_and_save_overhauls(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract overhaul data from DataFrame and save to database."""
        overhaul_count = 0
        
        # Find equipment-related columns
        equipment_col_map = self._map_equipment_columns(df.columns)
        
        for index, row in df.iterrows():
            try:
                # Get equipment data to link overhaul to equipment
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                
                if not equipment_data or not equipment_data.get('make_and_type') or not self._is_valid_equipment_record(equipment_data):
                    continue
                
                # Find the equipment ID
                equipment_id = self._find_equipment_id(equipment_data)
                if not equipment_id:
                    continue
                
                # Extract overhaul data for OH-I, OH-II, BOH (Basic Overhaul), and MLOH
                # Map BOH to OH-I and MLOH to OH-II to match database constraints
                overhaul_types = [
                    ('OH-I', 'OH-I'),
                    ('OH-II', 'OH-II'),
                    ('OH-I', 'BOH'),  # Basic Overhaul maps to OH-I
                    ('OH-II', 'MLOH')  # Major Life Overhaul maps to OH-II
                ]
                
                for overhaul_type, pattern in overhaul_types:
                    overhaul_data = self._extract_overhaul_data(row, pattern, overhaul_type, equipment_id)
                    
                    if overhaul_data:
                        # Save overhaul data to database
                        if self._save_overhaul_to_db(overhaul_data):
                            overhaul_count += 1
                            logger.info(f"Saved overhaul: {overhaul_type} for equipment ID {equipment_id}")
                
            except Exception as e:
                logger.error(f"Error processing overhaul for row {index} in {sheet_name}: {e}")
                continue
        
        return overhaul_count
    
    def _extract_overhaul_data(self, row: pd.Series, pattern: str, overhaul_type: str, equipment_id: int) -> Optional[Dict[str, Any]]:
        """Extract overhaul data for a specific overhaul type from a row."""
        from datetime import datetime, date
        
        overhaul_data = {
            'equipment_id': equipment_id,
            'overhaul_type': overhaul_type,
            'done_date': None,
            'due_date': None,
            'status': 'scheduled',
            'notes': f'Imported from Excel sheet as {pattern} (mapped to {overhaul_type})',
            'meter_reading': 0,
            'hours_reading': 0
        }
        
        # Find columns for this overhaul type
        done_date = None
        due_date = None
        
        for col in row.index:
            col_str = str(col).upper()
            
            # Check if this column is related to the overhaul pattern
            if pattern in col_str:
                if 'DONE' in col_str:
                    done_date = self._parse_date(row[col])
                elif 'DUE' in col_str:
                    due_date = self._parse_date(row[col])
        
        # Set dates first
        if done_date:
            overhaul_data['done_date'] = done_date
        
        if due_date:
            overhaul_data['due_date'] = due_date
        
        # Determine status based on dates and current time
        current_date = datetime.now().date()
        
        if done_date:
            # If we have a done date, it's completed
            overhaul_data['status'] = 'completed'
        elif due_date:
            # Parse due_date if it's a string
            if isinstance(due_date, str):
                try:
                    due_date_obj = datetime.strptime(due_date, '%Y-%m-%d').date()
                except:
                    try:
                        due_date_obj = datetime.strptime(due_date, '%Y-%m-%d %H:%M:%S').date()
                    except:
                        due_date_obj = current_date  # fallback
            else:
                due_date_obj = due_date
            
            # Check if overdue or due soon
            days_diff = (due_date_obj - current_date).days
            
            if days_diff < 0:
                # Past due date
                overhaul_data['status'] = 'overdue'
            elif days_diff <= 30:
                # Due within 30 days
                overhaul_data['status'] = 'warning'
            elif days_diff <= 90:
                # Due within 90 days
                overhaul_data['status'] = 'critical'
            else:
                # Future due date
                overhaul_data['status'] = 'scheduled'
        else:
            # No dates available
            overhaul_data['status'] = 'unknown'
        
        # Only return if we have at least one date
        return overhaul_data if (done_date or due_date) else None
    
    def _save_overhaul_to_db(self, overhaul_data: Dict[str, Any]) -> bool:
        """Save overhaul data to database."""
        import sqlite3
        import config
        
        try:
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Check if overhaul already exists for this equipment and type
            cursor.execute('''
                SELECT overhaul_id FROM overhauls 
                WHERE equipment_id = ? AND overhaul_type = ?
                ORDER BY overhaul_id DESC LIMIT 1
            ''', (overhaul_data['equipment_id'], overhaul_data['overhaul_type']))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing overhaul
                cursor.execute('''
                    UPDATE overhauls SET
                        done_date = ?,
                        due_date = ?,
                        status = ?,
                        notes = ?,
                        meter_reading = ?,
                        hours_reading = ?
                    WHERE overhaul_id = ?
                ''', (
                    overhaul_data['done_date'],
                    overhaul_data['due_date'],
                    overhaul_data['status'],
                    overhaul_data['notes'],
                    overhaul_data['meter_reading'],
                    overhaul_data['hours_reading'],
                    existing[0]
                ))
            else:
                # Insert new overhaul
                cursor.execute('''
                    INSERT INTO overhauls (
                        equipment_id, overhaul_type, done_date, due_date, 
                        status, notes, meter_reading, hours_reading
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    overhaul_data['equipment_id'],
                    overhaul_data['overhaul_type'],
                    overhaul_data['done_date'],
                    overhaul_data['due_date'],
                    overhaul_data['status'],
                    overhaul_data['notes'],
                    overhaul_data['meter_reading'],
                    overhaul_data['hours_reading']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving overhaul to database: {e}")
            return False

    def _extract_and_save_conditioning(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract conditioning/tyre maintenance data from DataFrame and save to database."""
        conditioning_count = 0
        
        # Find equipment-related columns
        equipment_col_map = self._map_equipment_columns(df.columns)
        
        for index, row in df.iterrows():
            try:
                # Get equipment data to link conditioning to equipment
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                
                if not equipment_data or not equipment_data.get('make_and_type') or not self._is_valid_equipment_record(equipment_data):
                    continue
                
                # Find equipment ID
                equipment_id = self._find_equipment_id(equipment_data)
                if not equipment_id:
                    logger.warning(f"Could not find equipment for conditioning data: {equipment_data.get('make_and_type')}")
                    continue
                
                # Extract tyre conditioning data
                conditioning_data = self._extract_conditioning_data(row, equipment_id, sheet_name)
                
                if conditioning_data:
                    # Save to database
                    success = self._save_conditioning_to_db(conditioning_data)
                    if success:
                        conditioning_count += 1
                        logger.info(f"Saved conditioning data for equipment ID {equipment_id}")
                    
            except Exception as e:
                logger.error(f"Error processing conditioning data in row {index} of {sheet_name}: {e}")
        
        return conditioning_count

    def _extract_conditioning_data(self, row: pd.Series, equipment_id: int, sheet_name: str) -> Optional[Dict[str, Any]]:
        """Extract conditioning/tyre maintenance data from a row."""
        conditioning_data = {
            'equipment_id': equipment_id,
            'tyre_rotation_kms': 5000,  # Default rotation interval
            'tyre_condition_kms': 0,
            'tyre_condition_years': 0,
            'last_rotation_date': None,
            'date_of_change': None,
            'quantity': 1
        }
        
        has_conditioning_data = False
        
        # Find conditioning-related columns
        for col in row.index:
            col_str = str(col).upper()
            
            # Extract tyre rotation interval
            if 'TYRE' in col_str and 'ROTATION' in col_str:
                rotation_value = self._parse_numeric(row[col])
                if rotation_value and rotation_value > 0:
                    conditioning_data['tyre_rotation_kms'] = int(rotation_value)
                    has_conditioning_data = True
            
            # Extract tyre condition KMS
            elif 'TYRE' in col_str and 'CONDITION' in col_str and 'KM' in col_str:
                condition_value = self._parse_numeric(row[col])
                if condition_value and condition_value > 0:
                    conditioning_data['tyre_condition_kms'] = int(condition_value)
                    has_conditioning_data = True
            
            # Extract tyre condition years (from patterns like "10 YRS", "5 YRS")
            elif 'TYRE' in col_str and 'CONDITION' in col_str and 'YR' in col_str:
                condition_text = str(row[col]) if pd.notna(row[col]) else ""
                # Extract years from text like "45000KM/10 YRS" or "5 YRS"
                import re
                year_match = re.search(r'(\d+)\s*YR', condition_text.upper())
                if year_match:
                    years = int(year_match.group(1))
                    conditioning_data['tyre_condition_years'] = years
                    has_conditioning_data = True
            
            # Extract tyre change date
            elif 'TYRE' in col_str and ('CHANGE' in col_str or 'DT' in col_str):
                change_date = self._parse_date(row[col])
                if change_date:
                    conditioning_data['date_of_change'] = change_date
                    conditioning_data['last_rotation_date'] = change_date  # Use same date for rotation
                    has_conditioning_data = True
        
        # Return conditioning data only if we found some relevant information
        return conditioning_data if has_conditioning_data else None

    def _save_conditioning_to_db(self, conditioning_data: Dict[str, Any]) -> bool:
        """Save conditioning data to the database."""
        try:
            import sqlite3
            import config
            
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Check if conditioning record already exists for this equipment
            cursor.execute('''
                SELECT tyre_maintenance_id 
                FROM tyre_maintenance 
                WHERE equipment_id = ?
            ''', (conditioning_data['equipment_id'],))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing record with the latest data from Excel
                cursor.execute('''
                    UPDATE tyre_maintenance SET
                        tyre_rotation_kms = ?,
                        tyre_condition_kms = ?,
                        tyre_condition_years = ?,
                        last_rotation_date = ?,
                        date_of_change = ?,
                        quantity = ?
                    WHERE equipment_id = ?
                ''', (
                    conditioning_data['tyre_rotation_kms'],
                    conditioning_data['tyre_condition_kms'],
                    conditioning_data['tyre_condition_years'],
                    conditioning_data['last_rotation_date'],
                    conditioning_data['date_of_change'],
                    conditioning_data['quantity'],
                    conditioning_data['equipment_id']
                ))
            else:
                # Insert new record
                cursor.execute('''
                    INSERT INTO tyre_maintenance (
                        equipment_id,
                        tyre_rotation_kms,
                        tyre_condition_kms,
                        tyre_condition_years,
                        last_rotation_date,
                        date_of_change,
                        quantity
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    conditioning_data['equipment_id'],
                    conditioning_data['tyre_rotation_kms'],
                    conditioning_data['tyre_condition_kms'],
                    conditioning_data['tyre_condition_years'],
                    conditioning_data['last_rotation_date'],
                    conditioning_data['date_of_change'],
                    conditioning_data['quantity']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving conditioning data to database: {e}")
            if 'conn' in locals():
                conn.close()
            return False

    def _extract_and_save_batteries(self, df: pd.DataFrame, sheet_name: str) -> int:
        """Extract battery data from DataFrame and save to database."""
        battery_count = 0
        
        # Find equipment-related columns
        equipment_col_map = self._map_equipment_columns(df.columns)
        
        for index, row in df.iterrows():
            try:
                # Get equipment data to link battery to equipment
                equipment_data = self._extract_equipment_data(row, equipment_col_map, sheet_name)
                
                if not equipment_data or not equipment_data.get('make_and_type') or not self._is_valid_equipment_record(equipment_data):
                    continue
                
                # Find equipment ID
                equipment_id = self._find_equipment_id(equipment_data)
                if not equipment_id:
                    continue
                
                # Extract battery data
                battery_data = self._extract_battery_data(row, equipment_id, sheet_name)
                
                if battery_data:
                    # Save battery data to database
                    if self._save_battery_to_db(battery_data):
                        battery_count += 1
                        logger.info(f"Saved battery data for equipment ID {equipment_id}")
                    
            except Exception as e:
                logger.error(f"Error processing battery data in row {index} of {sheet_name}: {e}")
        
        return battery_count

    def _extract_battery_data(self, row: pd.Series, equipment_id: int, sheet_name: str) -> Optional[Dict[str, Any]]:
        """Extract battery data from a row."""
        battery_data = {
            'equipment_id': equipment_id,
            'done_date': None,
            'custom_life_months': 24  # Default 24 months (2 years)
        }
        
        has_battery_data = False
        
        # Find battery-related columns
        for col in row.index:
            col_str = str(col).upper()
            
            # Extract battery change date
            if 'BTY' in col_str and ('CHANGE' in col_str or 'DT' in col_str):
                change_date = self._parse_date(row[col])
                if change_date:
                    battery_data['done_date'] = change_date
                    has_battery_data = True
            
            # Extract battery life (in months or years)
            elif 'BTY' in col_str and 'LIFE' in col_str:
                life_text = str(row[col]) if pd.notna(row[col]) else ""
                # Extract months from text like "24 months", "2 years", "24"
                import re
                
                # Try to find months first
                month_match = re.search(r'(\d+)\s*month', life_text.lower())
                if month_match:
                    months = int(month_match.group(1))
                    battery_data['custom_life_months'] = months
                    has_battery_data = True
                else:
                    # Try to find years and convert to months
                    year_match = re.search(r'(\d+)\s*year', life_text.lower())
                    if year_match:
                        years = int(year_match.group(1))
                        battery_data['custom_life_months'] = years * 12
                        has_battery_data = True
                    else:
                        # Try to extract just a number (assume months)
                        number_match = re.search(r'(\d+)', life_text)
                        if number_match:
                            number = int(number_match.group(1))
                            # If number is reasonable for months (6-60), use as months
                            # If too small (1-5), assume years
                            if 6 <= number <= 60:
                                battery_data['custom_life_months'] = number
                            elif 1 <= number <= 5:
                                battery_data['custom_life_months'] = number * 12
                            else:
                                battery_data['custom_life_months'] = 24  # Default
                            has_battery_data = True
        
        # Return battery data only if we found some relevant information
        return battery_data if has_battery_data else None

    def _save_battery_to_db(self, battery_data: Dict[str, Any]) -> bool:
        """Save battery data to the database."""
        try:
            import sqlite3
            import config
            
            conn = sqlite3.connect(config.DB_PATH)
            cursor = conn.cursor()
            
            # Check if battery record already exists for this equipment
            cursor.execute('''
                SELECT battery_id 
                FROM battery 
                WHERE equipment_id = ?
            ''', (battery_data['equipment_id'],))
            
            existing = cursor.fetchone()
            
            if existing:
                # Update existing record with the latest data from Excel
                cursor.execute('''
                    UPDATE battery SET
                        done_date = ?,
                        custom_life_months = ?
                    WHERE equipment_id = ?
                ''', (
                    battery_data['done_date'],
                    battery_data['custom_life_months'],
                    battery_data['equipment_id']
                ))
            else:
                # Insert new record
                cursor.execute('''
                    INSERT INTO battery (
                        equipment_id,
                        done_date,
                        custom_life_months
                    ) VALUES (?, ?, ?)
                ''', (
                    battery_data['equipment_id'],
                    battery_data['done_date'],
                    battery_data['custom_life_months']
                ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving battery data to database: {e}")
            if 'conn' in locals():
                conn.close()
            return False


def test_working_import(file_path: str):
    """Test the working importer."""
    print("Testing WORKING Excel Importer that saves real data...")
    
    importer = RobustExcelImporter()
    
    if not importer.initialize_staging():
        print("Failed to initialize database")
        return
    
    success, stats = importer.process_excel_file(file_path)
    print(f"Success: {success}")
    print(f"Stats: {stats}")
    
    # Verify data was actually saved
    try:
        import sqlite3
        import config
        conn = sqlite3.connect(config.DB_PATH)
        cursor = conn.cursor()
        
        # Check equipment
        cursor.execute('SELECT COUNT(*) FROM equipment')
        equipment_count = cursor.fetchone()[0]
        print(f"Equipment records in database: {equipment_count}")
        
        # Check fluids
        cursor.execute('SELECT COUNT(*) FROM fluids')
        fluids_count = cursor.fetchone()[0]
        print(f"Fluids records in database: {fluids_count}")
        
        # Check maintenance
        cursor.execute('SELECT COUNT(*) FROM maintenance')
        maintenance_count = cursor.fetchone()[0]
        print(f"Maintenance records in database: {maintenance_count}")
        
        # Show sample equipment records
        cursor.execute('SELECT equipment_id, make_and_type, ba_number, meterage_kms FROM equipment LIMIT 5')
        records = cursor.fetchall()
        print("\nSample equipment records:")
        for record in records:
            print(f"  ID: {record[0]}, Type: {record[1]}, BA: {record[2]}, KM: {record[3]}")
        
        # Show sample fluids records
        cursor.execute('''
            SELECT f.fluid_id, f.fluid_type, f.capacity_ltrs_kg, f.grade, e.make_and_type
            FROM fluids f 
            JOIN equipment e ON f.equipment_id = e.equipment_id 
            LIMIT 5
        ''')
        fluid_records = cursor.fetchall()
        print("\nSample fluids records:")
        for record in fluid_records:
            print(f"  ID: {record[0]}, Type: {record[1]}, Capacity: {record[2]}, Grade: {record[3]}, Equipment: {record[4]}")
        
        # Show sample maintenance records
        cursor.execute('''
            SELECT m.maintenance_id, m.maintenance_category, m.status, m.done_date, e.make_and_type
            FROM maintenance m 
            JOIN equipment e ON m.equipment_id = e.equipment_id 
            LIMIT 5
        ''')
        maintenance_records = cursor.fetchall()
        print("\nSample maintenance records:")
        for record in maintenance_records:
            print(f"  ID: {record[0]}, Category: {record[1]}, Status: {record[2]}, Done: {record[3]}, Equipment: {record[4]}")
        
        conn.close()
    except Exception as e:
        print(f"Error checking database: {e}")


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        test_working_import(sys.argv[1])
    else:
        print("Usage: python robust_excel_importer_working.py <file_path>") 