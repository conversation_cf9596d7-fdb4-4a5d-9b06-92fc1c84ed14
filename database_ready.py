from database import DB_PATH, dict_factory, init_db
import os
import sys
import sqlite3
from pathlib import Path
import traceback
import logging

# Ensure project root is in sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def ensure_db_ready():
    """Ensures the database is ready. Returns (success, first_run, corruption_recovered, error_message)"""
    corruption_recovered = False
    error_message = None
    try:
        db_dir = Path(DB_PATH).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        first_run = not os.path.exists(DB_PATH)
        # Try a basic connection
        try:
            conn = sqlite3.connect(DB_PATH, check_same_thread=False)
            conn.row_factory = dict_factory
            conn.execute("PRAGMA foreign_keys = ON")
            conn.close()
        except sqlite3.DatabaseError as db_err:
            # Corruption detected
            corruption_recovered = True
            error_message = f"Database file was corrupted and has been backed up. Details: {db_err}"
            corrupt_path = DB_PATH + ".corrupt.bak"
            if os.path.exists(DB_PATH):
                os.rename(DB_PATH, corrupt_path)
            # Create new DB
            conn = sqlite3.connect(DB_PATH, check_same_thread=False)
            conn.row_factory = dict_factory
            conn.execute("PRAGMA foreign_keys = ON")
            conn.close()
        # Now run full init
        success, _ = init_db()
        if not success:
            error_message = "init_db() failed unexpectedly. See db_debug.log for details."
            
        # Initialize policy tables if policy_service is available
        try:
            # Make sure module search path includes current directory
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)
                
            # Now try importing the policy_service module
            import policy_service
            policy_service.ensure_tables_exist()
        except ImportError as e:
            logging.warning(f"Policy module not available during database initialization. Some features will be disabled. Error: {e}")
        except Exception as e:
            logging.error(f"Error initializing policy tables: {e}")
            
        return success, first_run, corruption_recovered, error_message
    except Exception as e:
        tb = traceback.format_exc()
        return False, False, False, str(e) + "\n" + tb
