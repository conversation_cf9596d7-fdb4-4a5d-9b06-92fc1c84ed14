"""Maintenance management widget for the equipment inventory application."""
import logging
from datetime import datetime, date, timedelta

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, 
                           QLabel, QPushButton, QLineEdit, QComboBox, 
                           QSpinBox, QDoubleSpinBox, QDateEdit, QCheckBox,
                           QGroupBox, QFormLayout, QMessageBox, QSplitter,
                           QTextEdit, QDialog, QTabWidget, QDialogButtonBox)
from PyQt5.QtCore import Qt, QDate

import database
import utils
import config
from models import Equipment, Maintenance
from ui.custom_widgets import ReadOnlyTableWidget, StatusLabel

# Configure logger
logger = logging.getLogger('maintenance_widget')

class MaintenanceWidget(QWidget):
    """Main widget containing all maintenance sub-tabs."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the main maintenance widget with sub-tabs."""
        # Create main layout
        main_layout = QVBoxLayout(self)
        
        # Create tab widget for maintenance categories
        self.tab_widget = QTabWidget()
        
        # Create sub-widgets for each maintenance category
        self.tm1_widget = MaintenanceSubWidget(category='TM-1', parent=self)
        self.tm2_widget = MaintenanceSubWidget(category='TM-2', parent=self)
        
        # Regular maintenance tab with its own sub-tabs
        self.regular_maintenance_widget = RegularMaintenanceWidget(parent=self)
        
        # History tab for archived maintenance records
        from ui.maintenance_history_widget import MaintenanceHistoryWidget
        self.history_widget = MaintenanceHistoryWidget(parent=self)
        
        # Add tabs
        self.tab_widget.addTab(self.tm1_widget, "TM-1")
        self.tab_widget.addTab(self.tm2_widget, "TM-2")
        self.tab_widget.addTab(self.regular_maintenance_widget, "Regular Maintenance")
        self.tab_widget.addTab(self.history_widget, "History")
        
        # Connect tab changed signal
        self.tab_widget.currentChanged.connect(self.tab_changed)
        
        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)
        
    def load_data(self):
        """Load data for all maintenance sub-tabs."""
        logger.info("Loading data for all maintenance tabs")
        try:
            # Load data for currently visible tab first
            current_widget = self.tab_widget.currentWidget()
            if hasattr(current_widget, 'load_data'):
                current_widget.load_data()
                
            # Load data for other tabs
            for i in range(self.tab_widget.count()):
                widget = self.tab_widget.widget(i)
                if widget != current_widget and hasattr(widget, 'load_data'):
                    widget.load_data()
                    
        except Exception as e:
            logger.error(f"Error loading maintenance data: {e}")
    
    def tab_changed(self, index):
        """Handle tab change event."""
        current_widget = self.tab_widget.widget(index)
        if hasattr(current_widget, 'load_data'):
            current_widget.load_data()
    
    def switch_to_category(self, category):
        """Switch to the specified maintenance category tab."""
        try:
            if category == 'TM-1':
                self.tab_widget.setCurrentIndex(0)  # TM-1 tab
            elif category == 'TM-2':
                self.tab_widget.setCurrentIndex(1)  # TM-2 tab
            elif category in ['Yearly', 'Monthly']:
                self.tab_widget.setCurrentIndex(2)  # Regular Maintenance tab
                # Set the sub-tab within Regular Maintenance
                regular_widget = self.tab_widget.widget(2)
                if hasattr(regular_widget, 'set_active_tab'):
                    regular_widget.set_active_tab(category)
            
            # Load data for the current tab
            current_widget = self.tab_widget.currentWidget()
            if hasattr(current_widget, 'load_data'):
                current_widget.load_data()
                
            logger.info(f"Switched to maintenance category: {category}")
            
        except Exception as e:
            logger.error(f"Error switching to maintenance category {category}: {e}")
    
    def create_maintenance_from_equipment(self, equipment_id, maintenance_category):
        """Create new maintenance record from equipment tab."""
        try:
            # Switch to appropriate tab
            tab_mapping = {
                'TM-1': 0,
                'TM-2': 1,
                'Yearly': 2,  # Regular Maintenance tab
                'Monthly': 2  # Regular Maintenance tab
            }
            
            tab_index = tab_mapping.get(maintenance_category, 0)
            self.tab_widget.setCurrentIndex(tab_index)
            
            # If it's yearly or monthly, switch to appropriate sub-tab
            if maintenance_category in ['Yearly', 'Monthly']:
                self.regular_maintenance_widget.set_active_tab(maintenance_category)
                
            # Get the appropriate widget and create maintenance
            current_widget = self.tab_widget.currentWidget()
            if hasattr(current_widget, 'create_maintenance_from_equipment'):
                current_widget.create_maintenance_from_equipment(equipment_id, maintenance_category)
            elif hasattr(current_widget, 'get_current_sub_widget'):
                # For regular maintenance widget with sub-tabs
                sub_widget = current_widget.get_current_sub_widget()
                if sub_widget and hasattr(sub_widget, 'create_maintenance_from_equipment'):
                    sub_widget.create_maintenance_from_equipment(equipment_id, maintenance_category)
                    
        except Exception as e:
            logger.error(f"Error creating maintenance from equipment: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to create maintenance record: {str(e)}",
                QMessageBox.StandardButton.Ok
            )

class RegularMaintenanceWidget(QWidget):
    """Widget for regular maintenance with Yearly and Monthly sub-tabs."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the regular maintenance widget with yearly/monthly tabs."""
        # Create main layout
        main_layout = QVBoxLayout(self)
        
        # Create tab widget for yearly/monthly
        self.sub_tab_widget = QTabWidget()
        
        # Create sub-widgets
        self.yearly_widget = MaintenanceSubWidget(category='Yearly', parent=self)
        self.monthly_widget = MaintenanceSubWidget(category='Monthly', parent=self)
        
        # Add tabs (Yearly is default)
        self.sub_tab_widget.addTab(self.yearly_widget, "Yearly")
        self.sub_tab_widget.addTab(self.monthly_widget, "Monthly")
        
        # Set Yearly as default selection
        self.sub_tab_widget.setCurrentIndex(0)
        
        # Connect tab changed signal
        self.sub_tab_widget.currentChanged.connect(self.sub_tab_changed)
        
        # Add sub-tab widget to main layout
        main_layout.addWidget(self.sub_tab_widget)
        
    def load_data(self):
        """Load data for both yearly and monthly tabs."""
        try:
            self.yearly_widget.load_data()
            self.monthly_widget.load_data()
        except Exception as e:
            logger.error(f"Error loading regular maintenance data: {e}")
    
    def sub_tab_changed(self, index):
        """Handle sub-tab change event."""
        current_widget = self.sub_tab_widget.widget(index)
        if hasattr(current_widget, 'load_data'):
            current_widget.load_data()
    
    def set_active_tab(self, category):
        """Set the active sub-tab based on category."""
        if category == 'Yearly':
            self.sub_tab_widget.setCurrentIndex(0)
        elif category == 'Monthly':
            self.sub_tab_widget.setCurrentIndex(1)
    
    def get_current_sub_widget(self):
        """Get the currently active sub-widget."""
        return self.sub_tab_widget.currentWidget()

class MaintenanceSubWidget(QWidget):
    """Individual maintenance widget for each category (TM-1, TM-2, Yearly, Monthly)."""
    
    def __init__(self, category, parent=None):
        super().__init__(parent)
        self.category = category
        self.equipment_cache = {}  # Cache equipment data
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the maintenance sub-widget UI (same as original MaintenanceWidget)."""
        # Create main layout
        main_layout = QHBoxLayout(self)
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Create left panel (maintenance list)
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # Add category label
        category_config = config.MAINTENANCE_CATEGORIES.get(self.category, {'display': self.category})
        category_label = QLabel(f"<h3>{category_config['display']}</h3>")
        left_layout.addWidget(category_label)
        
        # Create filter controls
        filter_layout = QGridLayout()
        
        filter_layout.addWidget(QLabel("Search:"), 0, 0)
        self.search_field = QLineEdit()
        self.search_field.setPlaceholderText("Search by equipment or type...")
        self.search_field.textChanged.connect(self.filter_maintenance)
        filter_layout.addWidget(self.search_field, 0, 1, 1, 2)
        
        filter_layout.addWidget(QLabel("Show:"), 1, 0)
        self.filter_status = QComboBox()
        self.filter_status.addItems(["All", "Pending", "Upcoming (3 months)", "Warning (30 days)", "Critical (7 days)", "Waiting Decision", "Completed"])
        self.filter_status.currentIndexChanged.connect(self.load_data)
        filter_layout.addWidget(self.filter_status, 1, 1)
        
        self.filter_days = QSpinBox()
        self.filter_days.setMinimum(1)
        self.filter_days.setMaximum(365)
        self.filter_days.setValue(30)
        self.filter_days.setSuffix(" days")
        self.filter_days.setEnabled(False)
        self.filter_days.valueChanged.connect(self.load_data)
        filter_layout.addWidget(self.filter_days, 1, 2)
        
        # Connect status filter to enable/disable days filter
        self.filter_status.currentIndexChanged.connect(self.update_filter_days_state)
        
        left_layout.addLayout(filter_layout)
        
        # Create table for maintenance list
        self.maintenance_table = ReadOnlyTableWidget()
        self.maintenance_table.row_clicked.connect(self.maintenance_selected)
        left_layout.addWidget(self.maintenance_table)
        
        # Create buttons for maintenance actions
        button_layout = QHBoxLayout()
        self.add_button = QPushButton("Add New")
        self.edit_button = QPushButton("Edit")
        self.complete_button = QPushButton("Complete")
        self.delete_button = QPushButton("Delete")
        
        # Initial button states
        self.edit_button.setEnabled(False)
        self.complete_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        
        # Style the complete button
        self.complete_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        # Connect button signals
        self.add_button.clicked.connect(self.add_maintenance)
        self.edit_button.clicked.connect(self.edit_maintenance)
        self.complete_button.clicked.connect(self.complete_maintenance)
        self.delete_button.clicked.connect(self.delete_maintenance)
        
        # Add archive button
        self.archive_button = QPushButton("📦 Archive")
        self.archive_button.setToolTip("Archive completed maintenance records")
        self.archive_button.clicked.connect(self.archive_completed)
        self.archive_button.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.complete_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.archive_button)
        
        left_layout.addLayout(button_layout)
        
        # Create right panel (maintenance details)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Add maintenance details form
        self.create_maintenance_form(right_layout)
        
        # Add panels to splitter
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        
        # Set initial sizes
        splitter.setSizes([400, 600])
        
        # Add splitter to main layout
        main_layout.addWidget(splitter)
    
    def create_maintenance_form(self, parent_layout):
        """Create the maintenance details form."""
        # Create form group
        form_group = QGroupBox("Maintenance Details")
        form_layout = QFormLayout()
        
        # Create form fields
        self.id_field = QLineEdit()
        self.id_field.setReadOnly(True)
        
        self.equipment_field = QComboBox()
        
        self.maintenance_type_field = QComboBox()
        # Set maintenance types based on category
        maintenance_types = config.MAINTENANCE_TYPES.get(self.category, ["Other"])
        self.maintenance_type_field.addItems(maintenance_types)
        self.maintenance_type_field.setEditable(True)
        
        self.due_date_field = QDateEdit()
        self.due_date_field.setCalendarPopup(True)
        
        # Remove done date field and auto-calculate checkbox completely
        
        self.vintage_years_field = QDoubleSpinBox()
        self.vintage_years_field.setMinimum(0)
        self.vintage_years_field.setMaximum(100)
        self.vintage_years_field.setDecimals(2)
        self.vintage_years_field.setSuffix(" years")
        
        self.meterage_kms_field = QDoubleSpinBox()
        self.meterage_kms_field.setMinimum(0)
        self.meterage_kms_field.setMaximum(1000000)
        self.meterage_kms_field.setDecimals(2)
        self.meterage_kms_field.setSuffix(" km")
        
        # Add status display
        self.status_display = StatusLabel("Unknown", "unknown")
        
        # Add fields to form
        form_layout.addRow("ID:", self.id_field)
        form_layout.addRow("Equipment:", self.equipment_field)
        form_layout.addRow("Maintenance Type:", self.maintenance_type_field)
        form_layout.addRow("Due Date:", self.due_date_field)
        form_layout.addRow("Vintage (Years):", self.vintage_years_field)
        form_layout.addRow("Meterage (km):", self.meterage_kms_field)
        form_layout.addRow("Status:", self.status_display)
        
        form_group.setLayout(form_layout)
        
        # Create buttons for form actions
        button_layout = QHBoxLayout()
        self.save_button = QPushButton("Save")
        self.cancel_button = QPushButton("Cancel")
        
        self.save_button.clicked.connect(self.save_maintenance)
        self.cancel_button.clicked.connect(self.cancel_edit)
        
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        # Add form and buttons to parent layout
        parent_layout.addWidget(form_group)
        parent_layout.addLayout(button_layout)
        
        # Initially disable all fields
        self.set_form_enabled(False)
    
    # Removed update_due_date method - no longer needed without done date field
    
    # Removed toggle_due_date_calculation method - no longer needed without auto-calculate checkbox
    
    def update_filter_days_state(self, index):
        """Update the state of the days filter based on the status filter."""
        # Enable days filter only for "Critical (7 days)" status
        self.filter_days.setEnabled(index == 2)  # "Critical (7 days)" is index 2
    
    def load_data(self):
        """Load maintenance data for this category."""
        logger.info(f"Loading maintenance data for category: {self.category}")
        
        try:
            # Determine which maintenance records to load based on filter
            status_filter = self.filter_status.currentText()
            days = self.filter_days.value()
            
            if status_filter == "Pending":
                maintenance_list = Maintenance.get_pending_by_category(self.category)
            elif status_filter == "Critical (7 days)":
                maintenance_list = Maintenance.get_upcoming_by_category(self.category, 7)
            elif status_filter == "Upcoming (3 months)":
                # Get maintenance due within 31-90 days (3 months)
                query = """
                    SELECT m.*, e.make_and_type, e.ba_number
                    FROM maintenance m
                    JOIN equipment e ON m.equipment_id = e.equipment_id
                    WHERE m.maintenance_category = %s
                    AND m.due_date IS NOT NULL 
                    AND date(m.due_date) BETWEEN date('now', '+31 days') AND date('now', '+90 days')
                    AND (m.status != 'completed' OR m.status IS NULL)
                    AND (m.status != 'archived' OR m.status IS NULL)
                    ORDER BY m.due_date, e.make_and_type
                """
                maintenance_list = database.execute_query(query, (self.category,))
            elif status_filter == "Warning (30 days)":
                # Get maintenance due within 8-30 days
                query = """
                    SELECT m.*, e.make_and_type, e.ba_number
                    FROM maintenance m
                    JOIN equipment e ON m.equipment_id = e.equipment_id
                    WHERE m.maintenance_category = %s
                    AND m.due_date IS NOT NULL 
                    AND date(m.due_date) BETWEEN date('now', '+8 days') AND date('now', '+30 days')
                    AND (m.status != 'completed' OR m.status IS NULL)
                    AND (m.status != 'archived' OR m.status IS NULL)
                    ORDER BY m.due_date, e.make_and_type
                """
                maintenance_list = database.execute_query(query, (self.category,))
            elif status_filter == "Waiting Decision":
                maintenance_list = Maintenance.get_overdue_by_category(self.category)
            elif status_filter == "Completed":
                # Show records that are completed (have status='completed' OR have done_date OR actual_completion_date)
                query = """
                    SELECT m.*, e.make_and_type, e.ba_number
                    FROM maintenance m
                    JOIN equipment e ON m.equipment_id = e.equipment_id
                    WHERE m.maintenance_category = %s
                    AND (m.status = 'completed' OR m.done_date IS NOT NULL OR m.actual_completion_date IS NOT NULL)
                    AND (m.status != 'archived' OR m.status IS NULL)
                    ORDER BY m.actual_completion_date DESC, m.done_date DESC, e.make_and_type
                """
                maintenance_list = database.execute_query(query, (self.category,))
            else:
                # "All" - exclude archived records
                query = """
                    SELECT m.*, e.make_and_type, e.ba_number
                    FROM maintenance m
                    JOIN equipment e ON m.equipment_id = e.equipment_id
                    WHERE m.maintenance_category = %s
                    AND (m.status != 'archived' OR m.status IS NULL)
                    ORDER BY m.due_date, e.make_and_type
                """
                maintenance_list = database.execute_query(query, (self.category,))
            
            # Load equipment data for dropdown
            self.load_equipment_data()
            
            # Set up table headers
            headers = ["ID", "Equipment", "Type", "Done Date", "Due Date", "Status"]
            
            # Prepare data for table
            data = []
            if maintenance_list:
                for maintenance in maintenance_list:
                    # Format dates
                    done_date_val = maintenance.get('done_date')
                    due_date_val = maintenance.get('due_date')
                    
                    # Convert str to date if necessary (SQLite may return str)
                    if isinstance(done_date_val, str):
                        try:
                            done_date_val = datetime.strptime(done_date_val, config.DATE_FORMAT).date()
                        except Exception:
                            done_date_val = None
                    
                    if isinstance(due_date_val, str):
                        try:
                            due_date_val = datetime.strptime(due_date_val, config.DATE_FORMAT).date()
                        except Exception:
                            due_date_val = None
                    
                    done_date = utils.date_to_str(done_date_val) if done_date_val else "-"
                    due_date = utils.date_to_str(due_date_val) if due_date_val else "-"
                    
                    # Determine status - never show completed unless manually marked
                    # Show completed if there's a done_date or actual completion date
                    completion_date = maintenance.get('actual_completion_date')
                    done_date_val = maintenance.get('done_date')
                    
                    if completion_date or done_date_val:
                        # Completed via Complete button or has done_date filled
                        status = "completed"
                    elif due_date_val:
                        # Calculate based on due date, but change overdue to waiting decision
                        calculated_status = utils.get_maintenance_status(due_date_val)
                        if calculated_status == "overdue":
                            status = "waiting decision"
                        else:
                            status = calculated_status
                    else:
                        status = "unknown"
                    
                    # Add row data
                    row_data = {
                        "ID": maintenance['maintenance_id'],
                        "Equipment": utils.format_equipment_display(maintenance),
                        "Type": maintenance['maintenance_type'] or "",
                        "Done Date": done_date,
                        "Due Date": due_date,
                        "Status": status
                    }
                    data.append(row_data)
            
            # Set table data
            self.maintenance_table.set_data(headers, data, id_column=0)
            
            # Apply search filter
            self.filter_maintenance()
            
            logger.info(f"Loaded {len(data)} maintenance records for {self.category}")
        except Exception as e:
            logger.error(f"Error loading maintenance data for {self.category}: {e}")
    
    def load_equipment_data(self):
        """Load equipment data for dropdown."""
        try:
            # Get active equipment
            equipment_list = Equipment.get_active()
            
            # Clear dropdown
            self.equipment_field.clear()
            
            # Add equipment to dropdown and cache
            if equipment_list:
                self.equipment_cache = {}
                for equipment in equipment_list:
                    equipment_id = equipment['equipment_id']
                    from utils import format_equipment_for_dropdown
                    equipment_name = format_equipment_for_dropdown(equipment)
                    self.equipment_field.addItem(equipment_name, equipment_id)
                    self.equipment_cache[equipment_id] = equipment
        except Exception as e:
            logger.error(f"Error loading equipment data: {e}")
    
    def filter_maintenance(self):
        """Filter maintenance list based on search text."""
        search_text = self.search_field.text().lower()
        
        # Show all rows if search text is empty
        if not search_text:
            for row in range(self.maintenance_table.rowCount()):
                self.maintenance_table.setRowHidden(row, False)
            return
        
        # Hide rows that don't match search text
        for row in range(self.maintenance_table.rowCount()):
            match_found = False
            
            # Check equipment and type columns
            for col in [1, 2]:  # Equipment, Type
                item = self.maintenance_table.item(row, col)
                if item and search_text in item.text().lower():
                    match_found = True
                    break
            
            self.maintenance_table.setRowHidden(row, not match_found)
    
    def maintenance_selected(self, row):
        """Handle maintenance selection."""
        # Get selected maintenance ID
        maintenance_id = self.maintenance_table.get_selected_id()
        
        if maintenance_id:
            # Load maintenance details first to check status
            maintenance = Maintenance.get_by_id(maintenance_id)
            
            if maintenance:
                status = maintenance.get('status', 'scheduled')
                is_completed = status == 'completed'
                
                # Enable all buttons when maintenance is selected
                # Complete button is always enabled to give users full control
                self.edit_button.setEnabled(True)
                self.complete_button.setEnabled(True)  # Always enabled for user flexibility
                self.delete_button.setEnabled(True)
                
                # Load maintenance details
                self.load_maintenance_details(maintenance_id)
            else:
                # Disable all action buttons if maintenance not found
                self.edit_button.setEnabled(False)
                self.complete_button.setEnabled(False)
                self.delete_button.setEnabled(False)
                self.clear_form()
        else:
            # Disable all action buttons
            self.edit_button.setEnabled(False)
            self.complete_button.setEnabled(False)
            self.delete_button.setEnabled(False)
            
            # Clear form
            self.clear_form()
    
    def load_maintenance_details(self, maintenance_id):
        """Load maintenance details into the form with robust data validation."""
        try:
            # Get maintenance data with proper error handling
            maintenance = Maintenance.get_by_id(maintenance_id)
            
            if not maintenance:
                logger.error(f"No maintenance record found for ID: {maintenance_id}")
                self.clear_form()
                return
            
            logger.info(f"Loading maintenance details for ID {maintenance_id}: {maintenance}")
            
            # Set basic form values
            self.id_field.setText(str(maintenance.get('maintenance_id', '')))
            
            # Set equipment dropdown with validation
            equipment_id = maintenance.get('equipment_id')
            if equipment_id:
                index = self.equipment_field.findData(equipment_id)
                if index >= 0:
                    self.equipment_field.setCurrentIndex(index)
                else:
                    logger.warning(f"Equipment ID {equipment_id} not found in dropdown")
                    self.equipment_field.setCurrentIndex(-1)
            else:
                self.equipment_field.setCurrentIndex(-1)
            
            # Set maintenance type with validation
            maintenance_type = maintenance.get('maintenance_type', '')
            if maintenance_type:
                index = self.maintenance_type_field.findText(maintenance_type)
                if index >= 0:
                    self.maintenance_type_field.setCurrentIndex(index)
                else:
                    # Add custom maintenance type if not in predefined list
                    self.maintenance_type_field.addItem(maintenance_type)
                    self.maintenance_type_field.setCurrentText(maintenance_type)
            else:
                self.maintenance_type_field.setCurrentIndex(0)
            
            # Done date field removed - no longer handling done date in form
            
            # Handle due date with robust parsing
            due_date_raw = maintenance.get('due_date')
            has_due_date = False
            
            if due_date_raw:
                try:
                    # Parse due date from various formats
                    if isinstance(due_date_raw, str):
                        # Try different date formats
                        for fmt in ['%Y-%m-%d', '%d-%m-%Y', '%m-%d-%Y', '%Y/%m/%d', '%d/%m/%Y']:
                            try:
                                due_date_parsed = datetime.strptime(due_date_raw, fmt).date()
                                qdate = QDate(due_date_parsed.year, due_date_parsed.month, due_date_parsed.day)
                                self.due_date_field.setDate(qdate)
                                has_due_date = True
                                logger.info(f"Parsed due date: {due_date_raw} -> {due_date_parsed}")
                                break
                            except ValueError:
                                continue
                        
                        if not has_due_date:
                            # Try ISO format parsing as fallback
                            try:
                                due_date_parsed = date.fromisoformat(due_date_raw)
                                qdate = QDate(due_date_parsed.year, due_date_parsed.month, due_date_parsed.day)
                                self.due_date_field.setDate(qdate)
                                has_due_date = True
                                logger.info(f"Parsed due date (ISO): {due_date_raw} -> {due_date_parsed}")
                            except ValueError:
                                logger.error(f"Could not parse due date: {due_date_raw}")
                                self.due_date_field.clear()
                    elif isinstance(due_date_raw, date):
                        qdate = QDate(due_date_raw.year, due_date_raw.month, due_date_raw.day)
                        self.due_date_field.setDate(qdate)
                        has_due_date = True
                        logger.info(f"Set due date from date object: {due_date_raw}")
                    else:
                        logger.warning(f"Unknown due date format: {type(due_date_raw)} - {due_date_raw}")
                        self.due_date_field.clear()
                except Exception as e:
                    logger.error(f"Error parsing due date {due_date_raw}: {e}")
                    self.due_date_field.clear()
            else:
                # No due date in record
                self.due_date_field.clear()
                logger.info("No due date in record - field cleared")
            
            # Due date field is always editable (no auto-calculation)
            self.due_date_field.setReadOnly(False)
            logger.info("Due date field set to editable")
            
            # Set numeric fields with validation
            vintage_years = maintenance.get('vintage_years', 0)
            meterage_kms = maintenance.get('meterage_kms', 0)
            
            try:
                self.vintage_years_field.setValue(float(vintage_years) if vintage_years is not None else 0.0)
            except (ValueError, TypeError):
                self.vintage_years_field.setValue(0.0)
                logger.warning(f"Invalid vintage_years value: {vintage_years}")
            
            try:
                self.meterage_kms_field.setValue(float(meterage_kms) if meterage_kms is not None else 0.0)
            except (ValueError, TypeError):
                self.meterage_kms_field.setValue(0.0)
                logger.warning(f"Invalid meterage_kms value: {meterage_kms}")
            
            # Calculate and display status with proper logic
            status = self.calculate_maintenance_status(maintenance)
            self.status_display.setText(status.capitalize())
            self.status_display.setStatus(status)
            logger.info(f"Calculated status: {status}")
            
            # Set form fields to read-only
            self.set_form_enabled(False)
            
            logger.info(f"Successfully loaded maintenance details for ID {maintenance_id}")
            
        except Exception as e:
            logger.error(f"Error loading maintenance details for ID {maintenance_id}: {e}")
            self.clear_form()
            QMessageBox.critical(
                self,
                "Error Loading Details",
                f"Failed to load maintenance details:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def calculate_maintenance_status(self, maintenance):
        """Calculate maintenance status based on available data."""
        try:
            # Check for completion indicators
            actual_completion_date = maintenance.get('actual_completion_date')
            done_date = maintenance.get('done_date')
            status_field = maintenance.get('status', '').lower()
            
            # If explicitly marked as completed or has completion dates
            if (status_field == 'completed' or 
                actual_completion_date is not None or 
                done_date is not None):
                return "completed"
            
            # Check due date for status calculation
            due_date_raw = maintenance.get('due_date')
            if due_date_raw:
                try:
                    # Parse due date for status calculation
                    if isinstance(due_date_raw, str):
                        # Try different formats
                        due_date_parsed = None
                        for fmt in ['%Y-%m-%d', '%d-%m-%Y', '%m-%d-%Y']:
                            try:
                                due_date_parsed = datetime.strptime(due_date_raw, fmt).date()
                                break
                            except ValueError:
                                continue
                        
                        if not due_date_parsed:
                            try:
                                due_date_parsed = date.fromisoformat(due_date_raw)
                            except ValueError:
                                return "unknown"
                    elif isinstance(due_date_raw, date):
                        due_date_parsed = due_date_raw
                    else:
                        return "unknown"
                    
                    # Calculate status based on due date
                    calculated_status = utils.get_maintenance_status(due_date_parsed)
                    
                    # Convert overdue to waiting decision as per business logic
                    if calculated_status == "overdue":
                        return "waiting decision"
                    else:
                        return calculated_status
                        
                except Exception as e:
                    logger.error(f"Error calculating status from due date {due_date_raw}: {e}")
                    return "unknown"
            
            return "unknown"
            
        except Exception as e:
            logger.error(f"Error calculating maintenance status: {e}")
            return "unknown"
    
    def clear_form(self):
        """Clear the maintenance form."""
        self.id_field.clear()
        self.equipment_field.setCurrentIndex(-1)
        self.maintenance_type_field.setCurrentIndex(0)
        self.due_date_field.setDate(QDate.currentDate().addMonths(3))
        self.vintage_years_field.setValue(0)
        self.meterage_kms_field.setValue(0)
        self.status_display.setText("Unknown")
        self.status_display.setStatus("unknown")
    
    def set_form_enabled(self, enabled):
        """Enable or disable form fields."""
        self.equipment_field.setEnabled(enabled)
        self.maintenance_type_field.setEnabled(enabled)
        
        # Due date field is always editable when form is enabled
        self.due_date_field.setReadOnly(not enabled)
        
        self.vintage_years_field.setReadOnly(not enabled)
        self.meterage_kms_field.setReadOnly(not enabled)
        
        self.save_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)
    
    def add_maintenance(self):
        """Add new maintenance."""
        # Clear form
        self.clear_form()
        
        # Enable form fields
        self.set_form_enabled(True)
        
        # Focus on first field
        self.equipment_field.setFocus()
    
    def edit_maintenance(self):
        """Edit selected maintenance."""
        # Enable form fields
        self.set_form_enabled(True)
        
        # Focus on first editable field
        self.maintenance_type_field.setFocus()
    
    def complete_maintenance(self):
        """Complete selected maintenance."""
        maintenance_id = self.id_field.text()
        
        if not maintenance_id:
            return
        
        # Get maintenance data
        maintenance = Maintenance.get_by_id(int(maintenance_id))
        
        if not maintenance:
            QMessageBox.critical(
                self,
                "Error",
                "Selected maintenance record not found.",
                QMessageBox.StandardButton.Ok
            )
            return
        
        # Allow users to complete maintenance multiple times or update completion details
        # Remove the restrictive "already completed" check to enable full functionality
        
        # Open completion dialog
        from ui.dialogs import MaintenanceCompletionDialog
        dialog = MaintenanceCompletionDialog(maintenance, parent=self)
        
        if dialog.exec_() == QDialog.Accepted:
            try:
                # Get completion data
                completion_data = dialog.get_completion_data()
                
                # Create maintenance object and complete it
                maintenance_obj = Maintenance(
                    maintenance_id=int(maintenance_id),
                    equipment_id=maintenance['equipment_id'],
                    maintenance_type=maintenance['maintenance_type'],
                    done_date=maintenance['done_date'],
                    due_date=maintenance['due_date'],
                    vintage_years=maintenance['vintage_years'],
                    meterage_kms=maintenance['meterage_kms'],
                    maintenance_category=maintenance.get('maintenance_category', self.category)
                )
                
                # Complete the maintenance
                result = maintenance_obj.complete_maintenance(
                    completion_date=completion_data['completion_date'],
                    completion_meterage=completion_data['completion_meterage'],
                    notes=completion_data['notes'],
                    completed_by="Current User"  # TODO: Get actual user
                )
                
                if result:
                    # Update equipment meterage if requested
                    if completion_data['update_equipment_meterage']:
                        self.update_equipment_meterage(
                            maintenance['equipment_id'],
                            completion_data['completion_meterage']
                        )
                    
                    # Create fluid demand forecasts if requested
                    if completion_data['create_demand'] and completion_data['selected_fluids']:
                        self.create_fluid_demand_forecasts(
                            completion_data['selected_fluids'],
                            completion_data['fiscal_year']
                        )
                    
                    # Show success message
                    QMessageBox.information(
                        self,
                        "Success",
                        f"Maintenance completed successfully!\n\n"
                        f"Completion Date: {completion_data['completion_date']}\n"
                        f"Meterage: {completion_data['completion_meterage']} km\n"
                        f"{'Equipment meterage updated.' if completion_data['update_equipment_meterage'] else ''}\n"
                        f"{'Fluid demand forecasts created.' if completion_data['create_demand'] and completion_data['selected_fluids'] else ''}",
                        QMessageBox.StandardButton.Ok
                    )
                    
                    # Set filter to "All" to ensure completed maintenance remains visible
                    self.filter_status.setCurrentText("All")
                    
                    # Reload data and update display
                    self.load_data()
                    self.load_maintenance_details(int(maintenance_id))
                    
                    # Reload data to refresh status, but keep Complete button enabled
                    # This allows users to complete maintenance multiple times if needed
                else:
                    QMessageBox.critical(
                        self,
                        "Error",
                        "Failed to complete maintenance. Please try again.",
                        QMessageBox.StandardButton.Ok
                    )
            except Exception as e:
                logger.error(f"Error completing maintenance: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"An error occurred while completing maintenance:\n{str(e)}",
                    QMessageBox.StandardButton.Ok
                )
    
    def update_equipment_meterage(self, equipment_id, new_meterage):
        """Update equipment meterage."""
        try:
            from models import Equipment
            query = """
                UPDATE equipment 
                SET meterage_kms = %s 
                WHERE equipment_id = %s
            """
            database.execute_query(query, (new_meterage, equipment_id))
            logger.info(f"Updated equipment {equipment_id} meterage to {new_meterage}")
        except Exception as e:
            logger.error(f"Error updating equipment meterage: {e}")
    
    def create_fluid_demand_forecasts(self, selected_fluids, fiscal_year):
        """Create demand forecast records for selected fluids."""
        try:
            from models import DemandForecast
            
            created_count = 0
            
            for fluid in selected_fluids:
                # Get equipment data for calculations
                equipment = Equipment.get_by_id(fluid['equipment_id'])
                
                if equipment:
                    # Use fluid capacity as the requirement (simple approach)
                    capacity = fluid.get('capacity_ltrs_kg', 0)
                    top_up = fluid.get('addl_10_percent_top_up', 0)
                    units_held = equipment.units_held  # Access attribute directly, not via get()
                    
                    # Check if user wants to include top-up for this fluid
                    include_topup = fluid.get('include_topup', False)
                    
                    # Calculate total requirement based on user selection
                    if include_topup:
                        total_requirement = (capacity + top_up) * units_held
                    else:
                        total_requirement = capacity * units_held
                    
                    # If still 0, use a default minimum value
                    if total_requirement <= 0:
                        total_requirement = capacity if capacity > 0 else 1.0
                    
                    logger.info(f"Creating demand forecast - Fluid: {fluid.get('fluid_type')}, "
                               f"Capacity: {capacity}, Top-up: {top_up}, Include Top-up: {include_topup}, "
                               f"Units: {units_held}, Total Requirement: {total_requirement}")
                    
                    # Create demand forecast
                    topup_note = " (with 10% top-up)" if include_topup else ""
                    demand = DemandForecast(
                        fluid_id=fluid['fluid_id'],
                        fiscal_year=fiscal_year,
                        total_requirement=total_requirement,
                        remarks=f"Auto-generated from {self.category} maintenance completion{topup_note}"
                    )
                    
                    if demand.save():
                        created_count += 1
                        logger.info(f"Successfully created demand forecast for {fluid.get('fluid_type')}")
                    else:
                        logger.warning(f"Failed to save demand forecast for {fluid.get('fluid_type')}")
            
            logger.info(f"Created {created_count} demand forecast records for fiscal year {fiscal_year}")
            
        except Exception as e:
            logger.error(f"Error creating fluid demand forecasts: {e}")
            QMessageBox.warning(
                self,
                "Partial Success",
                f"Maintenance completed but there was an issue creating some fluid demand forecasts:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def delete_maintenance(self):
        """Delete selected maintenance."""
        maintenance_id = self.id_field.text()
        
        if not maintenance_id:
            return
        
        # Confirm deletion
        confirm = QMessageBox.question(
            self,
            "Confirm Deletion",
            "Are you sure you want to delete this maintenance record?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if confirm == QMessageBox.StandardButton.Yes:
            try:
                # Delete maintenance
                result = Maintenance.delete(int(maintenance_id))
                
                if result:
                    # Show success message
                    QMessageBox.information(
                        self,
                        "Success",
                        "Maintenance record deleted successfully.",
                        QMessageBox.StandardButton.Ok
                    )
                    
                    # Reload data
                    self.load_data()
                    
                    # Clear form
                    self.clear_form()
                    
                    # Disable edit and delete buttons
                    self.edit_button.setEnabled(False)
                    self.delete_button.setEnabled(False)
                else:
                    # Show error message
                    QMessageBox.critical(
                        self,
                        "Error",
                        "Failed to delete maintenance record. Please try again.",
                        QMessageBox.StandardButton.Ok
                    )
            except Exception as e:
                logger.error(f"Error deleting maintenance: {e}")
                QMessageBox.critical(
                    self,
                    "Error",
                    f"An error occurred: {str(e)}",
                    QMessageBox.StandardButton.Ok
                )
    
    def save_maintenance(self):
        """Save maintenance data."""
        # Get form values
        maintenance_id = self.id_field.text()
        equipment_id = self.equipment_field.currentData()
        maintenance_type = self.maintenance_type_field.currentText()
        
        # Get due date from field
        due_date_qdate = self.due_date_field.date()
        due_date = date(due_date_qdate.year(), due_date_qdate.month(), due_date_qdate.day())
        
        # Done date is not handled in the form - it's only set via Complete button or Excel import
        done_date = None
        
        vintage_years = self.vintage_years_field.value()
        meterage_kms = self.meterage_kms_field.value()
        
        # Validate form
        if not equipment_id or not maintenance_type:
            QMessageBox.warning(
                self,
                "Validation Error",
                "Equipment and Maintenance Type are required fields.",
                QMessageBox.StandardButton.Ok
            )
            return
        
        try:
            # Create maintenance object
            maintenance = Maintenance(
                maintenance_id=int(maintenance_id) if maintenance_id else None,
                equipment_id=equipment_id,
                maintenance_type=maintenance_type,
                done_date=done_date,
                due_date=due_date,
                vintage_years=vintage_years,
                meterage_kms=meterage_kms,
                maintenance_category=self.category
            )
            
            # Save maintenance
            result = maintenance.save()
            
            if result:
                # Show success message
                QMessageBox.information(
                    self,
                    "Success",
                    "Maintenance record saved successfully.",
                    QMessageBox.StandardButton.Ok
                )
                
                # Reload data
                self.load_data()
                
                # Load the saved maintenance details
                self.load_maintenance_details(result)
                
                # Disable form fields
                self.set_form_enabled(False)
                
                # Enable edit and delete buttons
                self.edit_button.setEnabled(True)
                self.delete_button.setEnabled(True)
            else:
                # Show error message
                QMessageBox.critical(
                    self,
                    "Error",
                    "Failed to save maintenance record. Please try again.",
                    QMessageBox.StandardButton.Ok
                )
        except Exception as e:
            logger.error(f"Error saving maintenance: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred: {str(e)}",
                QMessageBox.StandardButton.Ok
            )
    
    def cancel_edit(self):
        """Cancel maintenance edit."""
        # Get current maintenance ID
        maintenance_id = self.id_field.text()
        
        if maintenance_id:
            # Reload maintenance details
            self.load_maintenance_details(int(maintenance_id))
        else:
            # Clear form
            self.clear_form()
        
        # Disable form fields
        self.set_form_enabled(False)
    
    def create_maintenance_from_equipment(self, equipment_id, maintenance_category):
        """Create new maintenance record from equipment selection."""
        try:
            # Clear and enable form
            self.clear_form()
            self.set_form_enabled(True)
            
            # Set equipment
            index = self.equipment_field.findData(equipment_id)
            if index >= 0:
                self.equipment_field.setCurrentIndex(index)
            
            # Set default maintenance type for this category
            maintenance_types = config.MAINTENANCE_TYPES.get(maintenance_category, ["Other"])
            if maintenance_types:
                self.maintenance_type_field.setCurrentText(maintenance_types[0])
            
            # Focus on maintenance type field
            self.maintenance_type_field.setFocus()
            
        except Exception as e:
            logger.error(f"Error creating maintenance from equipment: {e}")
            QMessageBox.critical(
                self,
                "Error",
                f"Failed to create maintenance record: {str(e)}",
                QMessageBox.StandardButton.Ok
            )

    def archive_completed(self):
        """Archive completed maintenance records."""
        from datetime import datetime, date
        from calendar import month_name
        import json
        from models import MaintenanceArchive, Maintenance
        from PyQt5.QtWidgets import QInputDialog, QMessageBox
        
        try:
            # Get completed maintenance records for this category
            completed_records = Maintenance.get_completed_by_category(self.category)
            
            if not completed_records:
                QMessageBox.information(
                    self,
                    "No Records",
                    f"No completed maintenance records found for {self.category} category.",
                    QMessageBox.StandardButton.Ok
                )
                return
            
            # Determine date range for archive
            if not completed_records:
                return
            
            # Find date range of completed records
            dates = []
            for record in completed_records:
                completion_date = record.get('actual_completion_date') or record.get('done_date')
                if completion_date:
                    if isinstance(completion_date, str):
                        try:
                            dates.append(datetime.strptime(completion_date, '%Y-%m-%d').date())
                        except:
                            continue
                    else:
                        dates.append(completion_date)
            
            if not dates:
                QMessageBox.information(
                    self,
                    "No Dates",
                    "No valid completion dates found in the records.",
                    QMessageBox.StandardButton.Ok
                )
                return
            
            min_date = min(dates)
            max_date = max(dates)
            
            # Create archive dialog
            archive_dialog = ArchiveDialog(self.category, completed_records, min_date, max_date, self)
            if archive_dialog.exec_() == archive_dialog.Accepted:
                archive_data = archive_dialog.get_archive_data()
                
                if archive_data:
                    # Create and save the archive
                    archive = MaintenanceArchive(
                        archive_name=archive_data['name'],
                        archive_type=archive_data['type'],
                        maintenance_category=self.category,
                        period_start=archive_data['period_start'],
                        period_end=archive_data['period_end'],
                        created_by=archive_data.get('created_by', 'System'),
                        notes=archive_data.get('notes', '')
                    )
                    
                    # Set archived records
                    records_to_archive = archive_data['records']
                    archive.set_archived_records(records_to_archive)
                    
                    # Save archive
                    archive_id = archive.save()
                    
                    if archive_id:
                        # Mark records as archived (optional - keep them visible but mark as archived)
                        record_ids = [record['maintenance_id'] for record in records_to_archive]
                        archived_count = Maintenance.archive_records(record_ids)
                        
                        QMessageBox.information(
                            self,
                            "Archive Created",
                            f"Successfully created archive '{archive_data['name']}'.\n\n"
                            f"Archived {len(records_to_archive)} maintenance records.\n"
                            f"You can view this archive in the History tab.",
                            QMessageBox.StandardButton.Ok
                        )
                        
                        # Reload data to refresh the view
                        self.load_data()
                        
                        # Switch to history tab if parent has the method
                        main_window = self.window()
                        if hasattr(main_window, 'tab_widget'):
                            # Find the maintenance tab
                            for i in range(main_window.tab_widget.count()):
                                widget = main_window.tab_widget.widget(i)
                                if hasattr(widget, 'tab_widget'):  # This is the maintenance widget
                                    # Switch to history tab (index 3)
                                    widget.tab_widget.setCurrentIndex(3)
                                    # Load history data
                                    history_widget = widget.tab_widget.widget(3)
                                    if hasattr(history_widget, 'load_data'):
                                        history_widget.load_data()
                                    break
                    else:
                        QMessageBox.critical(
                            self,
                            "Archive Failed",
                            "Failed to create archive. Please try again.",
                            QMessageBox.StandardButton.Ok
                        )
                        
        except Exception as e:
            logger.error(f"Error archiving completed records: {e}")
            QMessageBox.critical(
                self,
                "Archive Error",
                f"An error occurred while creating the archive:\n{str(e)}",
                QMessageBox.StandardButton.Ok
            )

class ArchiveDialog(QDialog):
    """Dialog for creating maintenance archives."""
    
    def __init__(self, category, records, min_date, max_date, parent=None):
        super().__init__(parent)
        self.category = category
        self.records = records
        self.min_date = min_date
        self.max_date = max_date
        
        self.setWindowTitle(f"Archive {category} Maintenance Records")
        self.setModal(True)
        self.resize(500, 400)
        
        self.setup_ui()
        self.populate_defaults()
    
    def setup_ui(self):
        """Set up the archive dialog UI."""
        from PyQt5.QtWidgets import (QVBoxLayout, QHBoxLayout, QFormLayout, 
                                   QLineEdit, QComboBox, QDateEdit, QTextEdit,
                                   QCheckBox, QSpinBox, QLabel, QGroupBox,
                                   QDialogButtonBox)
        from PyQt5.QtCore import QDate
        
        layout = QVBoxLayout(self)
        
        # Archive info section
        info_group = QGroupBox("Archive Information")
        info_layout = QFormLayout()
        
        # Archive name
        self.name_edit = QLineEdit()
        info_layout.addRow("Archive Name:", self.name_edit)
        
        # Archive type
        self.type_combo = QComboBox()
        self.type_combo.addItems(["Monthly", "Yearly", "TM-1 (Half Yearly)", "TM-2 (Yearly)", "Custom"])
        self.type_combo.currentTextChanged.connect(self.update_archive_name)
        info_layout.addRow("Archive Type:", self.type_combo)
        
        # Period selection
        period_layout = QHBoxLayout()
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate(self.min_date))
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate(self.max_date))
        
        period_layout.addWidget(QLabel("From:"))
        period_layout.addWidget(self.start_date_edit)
        period_layout.addWidget(QLabel("To:"))
        period_layout.addWidget(self.end_date_edit)
        
        info_layout.addRow("Period:", period_layout)
        
        # Created by
        self.created_by_edit = QLineEdit()
        self.created_by_edit.setPlaceholderText("Enter your name (optional)")
        info_layout.addRow("Created By:", self.created_by_edit)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # Record selection section
        records_group = QGroupBox("Records to Archive")
        records_layout = QVBoxLayout()
        
        # Info about records
        self.records_info_label = QLabel(f"Found {len(self.records)} completed maintenance records.")
        records_layout.addWidget(self.records_info_label)
        
        # Include all option
        self.include_all_check = QCheckBox("Include all completed records")
        self.include_all_check.setChecked(True)
        records_layout.addWidget(self.include_all_check)
        
        records_group.setLayout(records_layout)
        layout.addWidget(records_group)
        
        # Notes section
        notes_group = QGroupBox("Archive Notes")
        notes_layout = QVBoxLayout()
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("Optional notes about this archive...")
        
        notes_layout.addWidget(self.notes_edit)
        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)
        
        # Dialog buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def populate_defaults(self):
        """Populate default values."""
        from datetime import datetime
        from calendar import month_name
        
        # Default archive name based on category and current date
        current_date = datetime.now()
        
        if self.category in ['Monthly']:
            # Monthly archive: "March 2025 Monthly Maintenance"
            month = month_name[current_date.month]
            year = current_date.year
            default_name = f"{month} {year} Monthly Maintenance"
            self.type_combo.setCurrentText("Monthly")
        elif self.category in ['Yearly']:
            # Yearly archive: "2025 Yearly Maintenance"
            year = current_date.year
            default_name = f"{year} Yearly Maintenance"
            self.type_combo.setCurrentText("Yearly")
        elif self.category == 'TM-1':
            # TM-1 archive: "March 2025 TM-1 Maintenance"
            month = month_name[current_date.month]
            year = current_date.year
            default_name = f"{month} {year} TM-1 Maintenance"
            self.type_combo.setCurrentText("TM-1 (Half Yearly)")
        elif self.category == 'TM-2':
            # TM-2 archive: "March 2025 TM-2 Maintenance"
            month = month_name[current_date.month]
            year = current_date.year
            default_name = f"{month} {year} TM-2 Maintenance"
            self.type_combo.setCurrentText("TM-2 (Yearly)")
        else:
            # Other categories: "March 2025 [Category] Maintenance"
            month = month_name[current_date.month]
            year = current_date.year
            default_name = f"{month} {year} {self.category} Maintenance"
            self.type_combo.setCurrentText("Custom")
        
        self.name_edit.setText(default_name)
    
    def update_archive_name(self):
        """Update archive name when type changes."""
        from datetime import datetime
        from calendar import month_name
        
        archive_type = self.type_combo.currentText()
        current_date = datetime.now()
        
        if archive_type == "Monthly":
            month = month_name[current_date.month]
            year = current_date.year
            new_name = f"{month} {year} Monthly Maintenance"
        elif archive_type == "Yearly":
            year = current_date.year
            new_name = f"{year} Yearly Maintenance"
        elif archive_type == "TM-1 (Half Yearly)":
            month = month_name[current_date.month]
            year = current_date.year
            new_name = f"{month} {year} TM-1 Maintenance"
        elif archive_type == "TM-2 (Yearly)":
            month = month_name[current_date.month]
            year = current_date.year
            new_name = f"{month} {year} TM-2 Maintenance"
        else:  # Custom
            month = month_name[current_date.month]
            year = current_date.year
            new_name = f"{month} {year} {self.category} Maintenance"
        
        self.name_edit.setText(new_name)
    
    def get_archive_data(self):
        """Get the archive data from the dialog."""
        if not self.name_edit.text().strip():
            QMessageBox.warning(
                self,
                "Invalid Input",
                "Please enter an archive name.",
                QMessageBox.StandardButton.Ok
            )
            return None
        
        # Determine which records to include
        if self.include_all_check.isChecked():
            records_to_archive = self.records
        else:
            # For now, include all - could add filtering later
            records_to_archive = self.records
        
        # Map display text to database-friendly type values
        archive_type = self.type_combo.currentText()
        type_mapping = {
            "Monthly": "monthly",
            "Yearly": "yearly", 
            "TM-1 (Half Yearly)": "tm-1",
            "TM-2 (Yearly)": "tm-2",
            "Custom": "custom"
        }
        
        return {
            'name': self.name_edit.text().strip(),
            'type': type_mapping.get(archive_type, archive_type.lower()),
            'period_start': self.start_date_edit.date().toPyDate(),
            'period_end': self.end_date_edit.date().toPyDate(),
            'created_by': self.created_by_edit.text().strip() or 'System',
            'notes': self.notes_edit.toPlainText().strip(),
            'records': records_to_archive
        }
