"""
Excel import utility for InventoryTracker application.
UPDATED: Now uses the new robust staging database approach for improved data validation and processing.
"""
import pandas as pd
import logging
import re
from datetime import date
from collections import defaultdict
import numpy as np
# Import models lazily to avoid database access during module import
from dateutil import parser as date_parser

# Import the new robust importer
from robust_excel_importer_working import RobustExcelImporter

def ensure_date_object(date_value):
    """Convert any date representation to a datetime.date object."""
    if date_value is None:
        return None
    if isinstance(date_value, date):
        return date_value
    try:
        if isinstance(date_value, str):
            return date.fromisoformat(date_value.split(' ')[0])
        elif isinstance(date_value, pd.Timestamp):
            return date_value.date()
        else:
            # Handle other types that might have a date method
            return date_value.date() if hasattr(date_value, 'date') else None
    except Exception as e:
        logging.debug(f"Could not convert {date_value} to date: {e}")
        return None

logger = logging.getLogger('excel_importer')

def parse_excel_date(val):
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A', 'nan', 'NaN'):
        return None
    
    try:
        # Handle pandas Timestamp and Python date objects
        if isinstance(val, (pd.Timestamp, date)):
            return val.strftime('%Y-%m-%d')
        
        # Handle numeric values (Excel serial numbers)
        if isinstance(val, (int, float)):
            # Excel dates are stored as days since 1900-01-01 (with 1900-01-01 = 1)
            # But Excel incorrectly treats 1900 as a leap year, so we need to adjust
            if 1 <= val <= 2958465:  # Valid Excel date range (1900-01-01 to 9999-12-31)
                try:
                    # Convert Excel serial number to date
                    excel_epoch = pd.Timestamp('1899-12-30')  # Excel's epoch (accounting for the leap year bug)
                    parsed_date = excel_epoch + pd.Timedelta(days=val)
                    return parsed_date.strftime('%Y-%m-%d')
                except:
                    pass  # Fall through to string parsing
        
        # Handle string values
        s = str(val).replace('\\', '/').replace('.', '/').replace('-', '/').strip().upper()
        
        # Skip obvious non-date strings
        if any(keyword in s for keyword in ['UNDER', 'MLOH', 'YRS', 'YEAR', 'MONTH', 'NA', 'NULL']):
            return None
        
        # Handle multiple dates in one cell (take the first one)
        if len(s.split()) > 1 and any(char in s for char in ['/', '-']):
            # Split by spaces and take the first date-like part
            parts = s.split()
            for part in parts:
                if any(char in part for char in ['/', '-']) and len(part) >= 6:
                    s = part
                    break
        
        # Handle DD/MM/YYYY format (common in your data like 23/07/2000)
        if '/' in s and len(s.split('/')) == 3:
            parts = s.split('/')
            try:
                if len(parts[0]) <= 2 and len(parts[1]) <= 2 and len(parts[2]) >= 2:
                    # Fix common typos like '23/03/20211' -> '23/03/2021'
                    if len(parts[2]) > 4:
                        parts[2] = parts[2][:4]
                    
                    # Validate day and month ranges
                    day, month, year = int(parts[0]), int(parts[1]), int(parts[2])
                    
                    # Convert 2-digit years to 4-digit (assumes 20xx for 00-30, 19xx for 31-99)
                    if year < 100:
                        year = 2000 + year if year <= 30 else 1900 + year
                    
                    # Validate date components
                    if 1 <= day <= 31 and 1 <= month <= 12 and 1900 <= year <= 2100:
                        # Check for invalid dates like 31/06 (June doesn't have 31 days)
                        try:
                            test_date = date(year, month, day)
                            return test_date.strftime('%Y-%m-%d')
                        except ValueError:
                            # Invalid date (e.g., 31/06/25)
                            return None
            except (ValueError, IndexError):
                pass
        
        # Try to parse with dateutil as fallback
        try:
            parsed_date = date_parser.parse(s, dayfirst=True)
            # Validate the parsed year is reasonable
            if 1900 <= parsed_date.year <= 2100:
                return parsed_date.strftime('%Y-%m-%d')
        except:
            pass
            
        return None
        
    except Exception as e:
        logger.debug(f"Could not parse date '{val}': {e}")
        return None

def parse_int(val):
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0
    s = str(val).replace(',', '').upper()
    
    # Handle range formats like "8 to 10 Year" - take the average
    if ' TO ' in s:
        try:
            parts = s.replace('YEAR', '').replace('YRS', '').replace('YR', '').split(' TO ')
            if len(parts) == 2:
                start = float(re.sub(r'[^0-9\.-]', '', parts[0].strip()))
                end = float(re.sub(r'[^0-9\.-]', '', parts[1].strip()))
                return int((start + end) / 2)  # Return average
        except Exception:
            pass
    
    # Handle complex formats like "5000 Hrs or 105000" - extract the larger number (likely KM)
    if ' OR ' in s:
        try:
            parts = s.split(' OR ')
            numbers = []
            for part in parts:
                # Extract numbers from each part
                num_str = re.sub(r'[^0-9\.-]', '', part.strip())
                if num_str:
                    numbers.append(float(num_str))
            if numbers:
                return int(max(numbers))  # Return the larger number
        except Exception:
            pass
    
    if 'YR' in s:
        s = s.split('YR')[0]
    if 'KM' in s:
        s = s.split('KM')[0]
    try:
        return int(float(s))
    except Exception:
        return 0

def parse_km_hrs(val):
    """Parse combined KM/HRS values like '625/267' or '1785/1756.2' into separate KM and HRS values."""
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0.0, 0.0
    
    s = str(val).replace(',', '').strip()
    
    # Handle slash-separated values (KM/HRS format)
    if '/' in s:
        try:
            parts = s.split('/')
            if len(parts) >= 2:
                km_part = parts[0].strip()
                hrs_part = parts[1].strip()
                
                # Extract numeric values
                km = float(re.sub(r'[^0-9\.-]', '', km_part)) if km_part else 0.0
                hrs = float(re.sub(r'[^0-9\.-]', '', hrs_part)) if hrs_part else 0.0
                
                return km, hrs
        except Exception:
            pass
    
    # Fallback: treat as single numeric value (assume it's KM)
    try:
        single_val = float(re.sub(r'[^0-9\.-]', '', s))
        return single_val, 0.0
    except Exception:
        return 0.0, 0.0

def parse_km_yrs(val):
    # Handles '10000 KM/10 YRS' or '10000 KM/10 YRS'
    if pd.isna(val) or str(val).strip() in ('', '#######', '-', 'NA', 'N/A'):
        return 0, 0
    s = str(val).upper().replace(' ', '')
    km, yrs = 0, 0
    if '/' in s:
        parts = s.split('/')
        for p in parts:
            if 'KM' in p:
                km = parse_int(p)
            elif 'YR' in p:
                yrs = parse_int(p)
    else:
        if 'KM' in s:
            km = parse_int(s)
        if 'YR' in s:
            yrs = parse_int(s)
    return km, yrs

def import_from_excel(file_path):
    """
    Import data from an Excel file into the database using the new robust staging approach.
    
    This function now uses the RobustExcelImporter which:
    - Analyzes Excel structure and data patterns
    - Validates all data through staging database
    - Provides comprehensive error handling and reporting
    - Supports all entity types including missing components (medium reset, discard criteria, conditioning)
    
    Args:
        file_path (str): Path to the Excel file to import
        
    Returns:
        dict: Import statistics with counts of imported records by type
    """
    logger.info(f"Starting robust Excel import from {file_path}")
    
    try:
        # Initialize the robust importer with staging database
        importer = RobustExcelImporter()
        
        # Initialize staging database
        if not importer.initialize_staging():
            logger.error("Failed to initialize staging database")
            return {
                'equipment': 0,
                'fluids': 0,
                'tyres': 0,
                'batteries': 0,
                'repairs': 0,
                'medium_resets': 0,
                'overhauls': 0,
                'maintenance': 0,
                'discard_criteria': 0,
                'skipped': 0,
                'errors': ['Failed to initialize staging database']
            }
        
        # Process the Excel file
        success, stats = importer.process_excel_file(file_path)
        
        if not success:
            logger.error(f"Excel import failed: {stats}")
            return {
                'equipment': 0,
                'fluids': 0,
                'tyres': 0,
                'batteries': 0,
                'repairs': 0,
                'medium_resets': 0,
                'overhauls': 0,
                'maintenance': 0,
                'discard_criteria': 0,
                'skipped': 0,
                'errors': [f"Import failed: {stats}"]
            }
        
        # Convert robust importer stats to legacy format for compatibility
        legacy_stats = {
            'equipment': stats.get('total_equipment', 0),
            'fluids': stats.get('total_fluids', 0),
            'tyres': stats.get('total_tyres', 0) + stats.get('total_conditioning', 0),  # Combine tyres and conditioning
            'batteries': stats.get('total_batteries', 0),
            'repairs': stats.get('total_repairs', 0),
            'overhauls': stats.get('total_overhauls', 0),
            'maintenance': stats.get('total_maintenance', 0),
            'discard_criteria': stats.get('total_discard_criteria', 0),
            'skipped': stats.get('total_skipped', 0)
        }
        
        logger.info(f"Robust Excel import completed successfully with stats: {legacy_stats}")
        return legacy_stats
        
    except Exception as e:
        logger.error(f"Error in robust Excel import: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {
            'equipment': 0,
            'fluids': 0,
            'tyres': 0,
            'batteries': 0,
            'repairs': 0,
            'medium_resets': 0,
            'overhauls': 0,
            'maintenance': 0,
            'discard_criteria': 0,
            'skipped': 0,
            'errors': [f"Import error: {str(e)}"]
        }

# Legacy functions kept for backward compatibility (but may not be used anymore)
def update_maintenance_categories():
    """Update maintenance categories for existing records (legacy function)."""
    logger.info("Legacy maintenance category update - now handled by robust importer")
    pass

def test_excel_column_detection(file_path):
    """Test Excel column detection (legacy function - now handled by analyzer)."""
    logger.info("Legacy column detection test - now handled by Excel analyzer")
    try:
        from excel_analyzer import ExcelAnalyzer
        analyzer = ExcelAnalyzer()
        analysis = analyzer.analyze_file(file_path)
        return analysis
    except Exception as e:
        logger.error(f"Error in column detection test: {e}")
        return None

# Add this at the end of the file for debugging
if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        test_excel_column_detection(sys.argv[1])
    else:
        print("Usage: python excel_importer.py <excel_file_path>")